using GameFramework;
using GameFramework.Event;

namespace Game.Hotfix
{
    /// <summary>
    /// 火车动画请求事件
    /// </summary>
    public sealed class TrainAnimationRequestEventArgs : GameEventArgs
    {
        /// <summary>
        /// 事件编号
        /// </summary>
        public static readonly int EventId = typeof(TrainAnimationRequestEventArgs).GetHashCode();

        /// <summary>
        /// 获取事件编号
        /// </summary>
        public override int Id => EventId;

        /// <summary>
        /// 火车站实体ID
        /// </summary>
        public int TrainStationId { get; private set; }

        /// <summary>
        /// 动画类型
        /// </summary>
        public TrainAnimationType AnimationType { get; private set; }

        /// <summary>
        /// 动画时长
        /// </summary>
        public float Duration { get; private set; }

        /// <summary>
        /// 用户数据
        /// </summary>
        public object UserData { get; private set; }

        /// <summary>
        /// 创建火车动画请求事件
        /// </summary>
        /// <param name="trainStationId">火车站实体ID</param>
        /// <param name="animationType">动画类型</param>
        /// <param name="duration">动画时长</param>
        /// <param name="userData">用户数据</param>
        /// <returns>事件实例</returns>
        public static TrainAnimationRequestEventArgs Create(int trainStationId, TrainAnimationType animationType, float duration = 2f, object userData = null)
        {
            TrainAnimationRequestEventArgs eventArgs = ReferencePool.Acquire<TrainAnimationRequestEventArgs>();
            eventArgs.TrainStationId = trainStationId;
            eventArgs.AnimationType = animationType;
            eventArgs.Duration = duration;
            eventArgs.UserData = userData;
            return eventArgs;
        }

        /// <summary>
        /// 清理事件
        /// </summary>
        public override void Clear()
        {
            TrainStationId = 0;
            AnimationType = TrainAnimationType.None;
            Duration = 0f;
            UserData = null;
        }
    }

    /// <summary>
    /// 火车动画状态事件
    /// </summary>
    public sealed class TrainAnimationStatusEventArgs : GameEventArgs
    {
        /// <summary>
        /// 事件编号
        /// </summary>
        public static readonly int EventId = typeof(TrainAnimationStatusEventArgs).GetHashCode();

        /// <summary>
        /// 获取事件编号
        /// </summary>
        public override int Id => EventId;

        /// <summary>
        /// 火车站实体ID
        /// </summary>
        public int TrainStationId { get; private set; }

        /// <summary>
        /// 动画类型
        /// </summary>
        public TrainAnimationType AnimationType { get; private set; }

        /// <summary>
        /// 动画状态
        /// </summary>
        public TrainAnimationStatus Status { get; private set; }

        /// <summary>
        /// 用户数据
        /// </summary>
        public object UserData { get; private set; }

        /// <summary>
        /// 创建火车动画状态事件
        /// </summary>
        /// <param name="trainStationId">火车站实体ID</param>
        /// <param name="animationType">动画类型</param>
        /// <param name="status">动画状态</param>
        /// <param name="userData">用户数据</param>
        /// <returns>事件实例</returns>
        public static TrainAnimationStatusEventArgs Create(int trainStationId, TrainAnimationType animationType, TrainAnimationStatus status, object userData = null)
        {
            TrainAnimationStatusEventArgs eventArgs = ReferencePool.Acquire<TrainAnimationStatusEventArgs>();
            eventArgs.TrainStationId = trainStationId;
            eventArgs.AnimationType = animationType;
            eventArgs.Status = status;
            eventArgs.UserData = userData;
            return eventArgs;
        }

        /// <summary>
        /// 清理事件
        /// </summary>
        public override void Clear()
        {
            TrainStationId = 0;
            AnimationType = TrainAnimationType.None;
            Status = TrainAnimationStatus.None;
            UserData = null;
        }
    }

    /// <summary>
    /// 火车动画类型
    /// </summary>
    public enum TrainAnimationType
    {
        None,           // 无
        Enter,          // 进站
        Exit,           // 出站
        Stop,           // 停止动画
        TeleportStart,  // 瞬移到起始点
        TeleportStay,   // 瞬移到停靠点
        TeleportEnd     // 瞬移到终点
    }

    /// <summary>
    /// 火车动画状态
    /// </summary>
    public enum TrainAnimationStatus
    {
        None,       // 无
        Started,    // 开始
        Completed,  // 完成
        Stopped,    // 停止
        Failed      // 失败
    }
}
