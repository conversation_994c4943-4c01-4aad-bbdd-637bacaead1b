using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    /// <summary>
    /// 火车控制UI界面示例
    /// 展示如何通过事件驱动火车动画
    /// </summary>
    public class UITrainControlForm : UIFormLogic
    {
        [Header("UI控件")]
        public Button btnEnterAnimation;
        public Button btnExitAnimation;
        public Button btnStopAnimation;
        public Button btnTeleportStart;
        public Button btnTeleportStay;
        public Button btnTeleportEnd;
        public Button btnPlaySequence;
        
        [Header("设置")]
        public float animationDuration = 2f;
        public int targetTrainStationId = 0; // 0表示所有火车站
        
        [Header("状态显示")]
        public Text txtStatus;
        public Text txtCurrentAnimation;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
            
            // 绑定按钮事件
            if (btnEnterAnimation != null)
                btnEnterAnimation.onClick.AddListener(OnEnterAnimationClick);
            
            if (btnExitAnimation != null)
                btnExitAnimation.onClick.AddListener(OnExitAnimationClick);
            
            if (btnStopAnimation != null)
                btnStopAnimation.onClick.AddListener(OnStopAnimationClick);
            
            if (btnTeleportStart != null)
                btnTeleportStart.onClick.AddListener(OnTeleportStartClick);
            
            if (btnTeleportStay != null)
                btnTeleportStay.onClick.AddListener(OnTeleportStayClick);
            
            if (btnTeleportEnd != null)
                btnTeleportEnd.onClick.AddListener(OnTeleportEndClick);
            
            if (btnPlaySequence != null)
                btnPlaySequence.onClick.AddListener(OnPlaySequenceClick);
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            
            // 订阅火车动画事件
            EL_TrainStation.OnTrainAnimationStarted += OnTrainAnimationStarted;
            EL_TrainStation.OnTrainAnimationCompleted += OnTrainAnimationCompleted;
            EL_TrainStation.OnTrainAnimationStopped += OnTrainAnimationStopped;
            
            UpdateStatusText("火车控制界面已打开");
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            
            // 取消订阅火车动画事件
            EL_TrainStation.OnTrainAnimationStarted -= OnTrainAnimationStarted;
            EL_TrainStation.OnTrainAnimationCompleted -= OnTrainAnimationCompleted;
            EL_TrainStation.OnTrainAnimationStopped -= OnTrainAnimationStopped;
        }

        #region 按钮事件处理

        private void OnEnterAnimationClick()
        {
            PlayTrainEnterAnimation();
        }

        private void OnExitAnimationClick()
        {
            PlayTrainExitAnimation();
        }

        private void OnStopAnimationClick()
        {
            StopTrainAnimation();
        }

        private void OnTeleportStartClick()
        {
            TeleportTrainToStart();
        }

        private void OnTeleportStayClick()
        {
            TeleportTrainToStay();
        }

        private void OnTeleportEndClick()
        {
            TeleportTrainToEnd();
        }

        private void OnPlaySequenceClick()
        {
            PlayTrainSequence();
        }

        #endregion

        #region 火车动画控制方法

        /// <summary>
        /// 播放火车进站动画
        /// </summary>
        public void PlayTrainEnterAnimation()
        {
            EL_TrainStation trainStation = FindTrainStation();
            if (trainStation != null)
            {
                trainStation.PlayTrainEnterAnimation(animationDuration);
                UpdateStatusText("请求播放进站动画");
            }
            else
            {
                UpdateStatusText("未找到火车站");
            }
        }

        /// <summary>
        /// 播放火车出站动画
        /// </summary>
        public void PlayTrainExitAnimation()
        {
            EL_TrainStation trainStation = FindTrainStation();
            if (trainStation != null)
            {
                trainStation.PlayTrainExitAnimation(animationDuration);
                UpdateStatusText("请求播放出站动画");
            }
            else
            {
                UpdateStatusText("未找到火车站");
            }
        }

        /// <summary>
        /// 停止火车动画
        /// </summary>
        public void StopTrainAnimation()
        {
            EL_TrainStation trainStation = FindTrainStation();
            if (trainStation != null)
            {
                trainStation.StopTrainAnimation();
                UpdateStatusText("请求停止动画");
            }
            else
            {
                UpdateStatusText("未找到火车站");
            }
        }

        /// <summary>
        /// 瞬移火车到起始点
        /// </summary>
        public void TeleportTrainToStart()
        {
            EL_TrainStation trainStation = FindTrainStation();
            if (trainStation != null)
            {
                trainStation.TeleportTrainToStation(TrainStationType.Start);
                UpdateStatusText("火车瞬移到起始点");
            }
            else
            {
                UpdateStatusText("未找到火车站");
            }
        }

        /// <summary>
        /// 瞬移火车到停靠点
        /// </summary>
        public void TeleportTrainToStay()
        {
            EL_TrainStation trainStation = FindTrainStation();
            if (trainStation != null)
            {
                trainStation.TeleportTrainToStation(TrainStationType.Stay);
                UpdateStatusText("火车瞬移到停靠点");
            }
            else
            {
                UpdateStatusText("未找到火车站");
            }
        }

        /// <summary>
        /// 瞬移火车到终点
        /// </summary>
        public void TeleportTrainToEnd()
        {
            EL_TrainStation trainStation = FindTrainStation();
            if (trainStation != null)
            {
                trainStation.TeleportTrainToStation(TrainStationType.End);
                UpdateStatusText("火车瞬移到终点");
            }
            else
            {
                UpdateStatusText("未找到火车站");
            }
        }

        /// <summary>
        /// 播放完整的火车序列（进站-停留-出站）
        /// </summary>
        public void PlayTrainSequence()
        {
            EL_TrainStation trainStation = FindTrainStation();
            if (trainStation != null)
            {
                UpdateStatusText("开始播放火车序列");
                
                // 先瞬移到起始点
                trainStation.TeleportTrainToStation(TrainStationType.Start);
                
                // 延迟0.5秒后开始进站
                Invoke(nameof(StartEnterAnimation), 0.5f);
            }
            else
            {
                UpdateStatusText("未找到火车站");
            }
        }

        private void StartEnterAnimation()
        {
            EL_TrainStation trainStation = FindTrainStation();
            if (trainStation != null)
            {
                trainStation.PlayTrainEnterAnimation(animationDuration, () => {
                    // 进站完成后停留3秒，然后出站
                    Invoke(nameof(StartExitAnimation), 3f);
                });
            }
        }

        private void StartExitAnimation()
        {
            EL_TrainStation trainStation = FindTrainStation();
            if (trainStation != null)
            {
                trainStation.PlayTrainExitAnimation(animationDuration);
            }
        }

        #endregion

        #region 火车动画事件处理

        private void OnTrainAnimationStarted(int trainStationId, string animationType)
        {
            UpdateCurrentAnimationText($"动画开始: {animationType}");
            ColorLog.Green($"火车站 {trainStationId} 开始播放 {animationType} 动画");
        }

        private void OnTrainAnimationCompleted(int trainStationId, string animationType)
        {
            UpdateCurrentAnimationText($"动画完成: {animationType}");
            ColorLog.Green($"火车站 {trainStationId} 完成 {animationType} 动画");
        }

        private void OnTrainAnimationStopped(int trainStationId, string animationType)
        {
            UpdateCurrentAnimationText($"动画停止: {animationType}");
            ColorLog.Yellow($"火车站 {trainStationId} 停止 {animationType} 动画");
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 查找火车站实体
        /// </summary>
        private EL_TrainStation FindTrainStation()
        {
            if (targetTrainStationId != 0)
            {
                // 查找指定ID的火车站
                Entity entity = GameEntry.Entity.GetGameEntity(targetTrainStationId);
                return entity as EL_TrainStation;
            }
            else
            {
                // 查找第一个火车站（示例实现）
                // 实际项目中可能需要更复杂的查找逻辑
                return FindObjectOfType<EL_TrainStation>();
            }
        }

        /// <summary>
        /// 更新状态文本
        /// </summary>
        private void UpdateStatusText(string status)
        {
            if (txtStatus != null)
            {
                txtStatus.text = $"状态: {status}";
            }
            ColorLog.Green($"UI状态: {status}");
        }

        /// <summary>
        /// 更新当前动画文本
        /// </summary>
        private void UpdateCurrentAnimationText(string animation)
        {
            if (txtCurrentAnimation != null)
            {
                txtCurrentAnimation.text = $"当前: {animation}";
            }
        }

        #endregion
    }
}
