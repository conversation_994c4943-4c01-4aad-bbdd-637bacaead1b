%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Battle_mat_wangge_2
  m_Shader: {fileID: 4800000, guid: 910230d32064ba241b896b88312ca849, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 14d0d0b42c9d5934d807f14c159e1f83, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample0:
        m_Texture: {fileID: 2800000, guid: be2a85721f820a844851f7875dd5f367, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample1:
        m_Texture: {fileID: 2800000, guid: 14d0d0b42c9d5934d807f14c159e1f83, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 1, y: 0}
    - _TextureSample2:
        m_Texture: {fileID: 2800000, guid: 81b29c050d0f8ea47b5a4eb0233b9f88, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _ColorMask: 15
    - _Float0: 1
    - _Float2: -2
    - _InvFade: 1
    - _SpeedX: 0
    - _SpeedY: 0
    - _Stencil: 0
    - _StencilComp: 8
    - _StencilOp: 0
    - _StencilReadMask: 255
    - _StencilWriteMask: 255
    m_Colors:
    - _Color0: {r: 1.8867924, g: 1.8867924, b: 1.8867924, a: 1}
    - _Color1: {r: 1, g: 1, b: 1, a: 0}
    - _TintColor: {r: 0.6039216, g: 0.6039216, b: 0.6039216, a: 0.52156866}
    - _Vector0: {r: 0, g: -1, b: 0, a: 0}
    - _Vector1: {r: 0, g: 0, b: 0, a: 0}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
