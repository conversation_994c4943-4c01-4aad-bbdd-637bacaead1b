using System;
using System.Collections;
using System.Collections.Generic;
using Game.Hotfix.Config;
using GameFramework.Resource;
using Sirenix.Utilities;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIHeroNewForm : UGuiFormEx
    {
        private UIImage winBg;
        private UIImage topBg;
        private UIImage lightImg;
        private UIImage starImg;
        private UIImage downBg;
        private GameObject toyObj;

        private itemid heroConfigId;
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            winBg = transform.Find("winBg").GetComponent<UIImage>();
            topBg = transform.Find("topBg").GetComponent<UIImage>();
            lightImg = transform.Find("lightImg").GetComponent<UIImage>();
            starImg = transform.Find("starImg").GetComponent<UIImage>();
            downBg = transform.Find("downBg").GetComponent<UIImage>();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            var heroId = (itemid)userData;
            heroConfigId = heroId;
            OnUpdateInfo(heroId);

            Timers.Instance.Remove("UIHeroNewForm");
            Timers.Instance.Add("UIHeroNewForm", 4, (param) =>
            {
                m_btnMask.gameObject.SetActive(false);
            });
            m_btnMask.gameObject.SetActive(true);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            HideEffect();
            base.OnClose(isShutdown, userData);
            Timers.Instance.Remove("UIHeroNewForm");
            CheckRecruitAnimLogic();
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnUpdateInfo(itemid heroId)
        {
            var heroVo = GameEntry.LogicData.HeroData.GetHeroModule(heroId);
            if (heroVo == null) return;

            var list = heroVo.HeroSpineList;
            if (list != null && list.Count >= 2)
            {
                GameEntry.Resource.LoadAsset(list[0], typeof(Material), new LoadAssetCallbacks((assetName, asset, duration, userData) =>
                {
                    var material = asset as Material;
                    GameEntry.Resource.LoadAsset(list[1], typeof(Spine.Unity.SkeletonDataAsset), new LoadAssetCallbacks((assetName, asset, duration, userData) =>
                    {
                        var skeletonDataAsset = asset as Spine.Unity.SkeletonDataAsset;
                        m_spuiHero.material = material;
                        m_spuiHero.skeletonDataAsset = skeletonDataAsset;
                        m_spuiHero.Initialize(true);

                        m_spuiRole.material = material;
                        m_spuiRole.skeletonDataAsset = skeletonDataAsset;
                        m_spuiRole.Initialize(true);
                    }));
                }));
            }

            if (toyObj != null) Destroy(toyObj);

            var heroConfig = heroVo.GetHeroConfig();
            if (heroConfig != null && !heroConfig.hero_animation.IsNullOrWhitespace())
            {
                GameEntry.Resource.LoadAsset(heroConfig.hero_animation, new LoadAssetCallbacks((assetName, asset, duration, userData) =>
                {
                    var prefab = asset as GameObject;
                    toyObj = Instantiate(prefab, m_goToy.transform);
                }));
            }

            var quality = heroVo.Quality;
            var isFive = quality == 5;
            winBg.SetImage(isFive ? "Sprite/ui_jianzhu_jiuguan/xinjshq_jin_bg.png" : "Sprite/ui_jianzhu_jiuguan/xinjshq_zi_bg.png");
            topBg.SetImage(isFive ? "Sprite/ui_jianzhu_jiuguan/xinjshq_jin_di2.png" : "Sprite/ui_jianzhu_jiuguan/xinjshq_zi_di2.png");
            m_imgRoleBg.SetImage(isFive ? "Sprite/ui_jianzhu_jiuguan/xinjshq_jin_di2.png" : "Sprite/ui_jianzhu_jiuguan/xinjshq_zi_di2.png");
            lightImg.SetImage(isFive ? "Sprite/ui_jianzhu_jiuguan/xinjshq_jin_guang.png" : "Sprite/ui_jianzhu_jiuguan/xinjshq_zi_guang.png");
            starImg.SetImage(isFive ? "Sprite/ui_jianzhu_jiuguan/xinjshq_jin_xing.png" : "Sprite/ui_jianzhu_jiuguan/xinjshq_zi_xing.png");
            downBg.SetImage(isFive ? "Sprite/ui_jianzhu_jiuguan/xinjshq_jin_bg_wl.png" : "Sprite/ui_jianzhu_jiuguan/xinjshq_zi_bg_wl.png");
            m_imgServicesBg.SetImage(isFive ? "Sprite/ui_jianzhu_jiuguan/xinjshq_jin_kuang.png" : "Sprite/ui_jianzhu_jiuguan/xinjshq_zi_kuang.png");
            m_imgLine.SetImage(isFive ? "Sprite/ui_jianzhu_jiuguan/xinjshq_jin_di1.png" : "Sprite/ui_jianzhu_jiuguan/xinjshq_zi_di1.png");

            var heroData = GameEntry.LogicData.HeroData;
            m_imgQuality.SetImage(heroData.GetQualityImgPath(heroVo.Quality), true);
            m_imgServices.SetImage(heroData.GetServicesImgPath(heroVo.Services), true);

            m_txtNickname.text = heroVo.Nickname;
            m_txtName.text = heroVo.Name;

            var count = m_transSkill.childCount;
            var isActive = heroVo.IsActive;
            for (int i = 0; i < count; i++)
            {
                var skillTrans = m_transSkill.GetChild(i);
                var index = i + 1;
                var skillGroup = heroVo.GetSkillGroup(index);
                var config = skillGroup > 0 ? heroVo.GetHeroSkillConfig(index) : null;
                if (config != null)
                {
                    var btn = skillTrans.GetComponent<UIButton>();
                    var icon = skillTrans.Find("icon").GetComponent<UIImage>();
                    var levelTxt = skillTrans.Find("levelBg/levelTxt").GetComponent<UIText>();

                    var skillLv = 1;
                    var maxLv = heroVo.GetSkillLvLimit(index);
                    if (skillLv < maxLv)
                    {
                        levelTxt.text = skillLv + "";
                    }
                    else
                    {
                        levelTxt.text = ToolScriptExtend.GetLang(711363);
                    }

                    if (!string.IsNullOrEmpty(config.icon))
                        icon.SetImage(config.icon);

                    btn.onClick.RemoveAllListeners();
                    btn.onClick.AddListener(() =>
                    {
                        // 技能动画
                    });
                }
                skillTrans.gameObject.SetActive(config != null);
            }
        }

        private void OnBtnMaskClick()
        {

        }

        private void CheckRecruitAnimLogic()
        {
            var form = GameEntry.UI.GetUIForm(EnumUIForm.UIRecruitAnimForm);
            if (form != null)
            {
                GameEntry.UI.RefreshUIForm(EnumUIForm.UIRecruitAnimForm,(1,(int)heroConfigId));
            }
        }
        
        private void HideEffect()
        {
            var form = GameEntry.UI.GetUIForm(EnumUIForm.UIRecruitAnimForm);
            if (form != null)
            {
                var target = form as UIRecruitAnimForm;
                if (target != null)
                {
                    target.CloseShowedNodeEffect();
                }
            }
        }
        
    }
}
