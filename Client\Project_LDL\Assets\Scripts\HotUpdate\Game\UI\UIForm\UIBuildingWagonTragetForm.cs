using System;
using System.Collections;
using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIBuildingWagonTragetForm : UGuiFormEx
    {
        private int minBoxId;
        private List<int> getList;
        private List<dungeon_reward> rewardList;
        private int nextIndex;
        private int maxIndex;
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            rewardList = GameEntry.LDLTable.GetTable<dungeon_reward>();

            m_TableViewV.GetItemCount = () => rewardList.Count;
            m_TableViewV.GetItemGo = () => m_goItem;
            m_TableViewV.UpdateItemCell = OnUpdateItem;
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            var param = (ValueTuple<int, List<int>>)userData;
            minBoxId = param.Item1;
            getList = param.Item2;
            OnUpdateInfo();
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);

            if (GameEntry.UI.HasUIForm(EnumUIForm.UIBuildingWagonForm))
                GameEntry.UI.RefreshUIForm(EnumUIForm.UIBuildingWagonForm, 0);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnUpdateInfo(bool isReload = false)
        {
            var curId = GameEntry.LogicData.DungeonData.CurDungeonId;
            var nextIdx = -1;
            maxIndex = rewardList.Count;
            for (int i = 0; i < maxIndex; i++)
            {
                if (rewardList[i].dungeon_id > curId)
                {
                    nextIdx = i + 1;
                    break;
                }
            }
            nextIndex = nextIdx != -1 ? nextIdx : maxIndex;

            var hasReward = nextIndex - minBoxId - 1 > getList.Count;
            m_btnClaim.gameObject.SetActive(hasReward);

            if (isReload) { m_TableViewV.ReloadData(); }
            else { m_TableViewV.InitTableViewByIndex(0); }
        }

        private void OnUpdateItem(int index, GameObject go)
        {
            var rootTrans = go.transform.Find("bg");
            var bg = rootTrans.GetComponent<UIImage>();
            var titleTxt = rootTrans.Find("titleTxt").GetComponent<UIText>();
            var nonetxt = rootTrans.Find("m_txtAuto1100468").gameObject;
            var getBtn = rootTrans.Find("getBtn").GetComponent<UIButton>();
            var btnTxt = rootTrans.Find("getBtn/btnTxt").GetComponent<UIText>();
            var tableViewH = rootTrans.Find("tableViewH").GetComponent<Mosframe.TableViewH>();
            var rewardItem = rootTrans.Find("rewardItem").gameObject;

            var data = rewardList[index];
            titleTxt.text = ToolScriptExtend.GetLangFormat(1100467, data.dungeon_id + "");

            var state = 0; // 0:不可领取 1:领取 2:已领取
            var showIndex = index + 1;
            if (showIndex <= minBoxId)
            {
                state = 2;
            }
            else
            {
                if (data.dungeon_id > GameEntry.LogicData.DungeonData.CurDungeonId) { state = 0; }
                else if (getList.Contains(showIndex)) { state = 2; }
                else { state = 1; }
            }
            btnTxt.text = state == 1 ? ToolScriptExtend.GetLang(1100273) : ToolScriptExtend.GetLang(1100262);
            bg.SetImage(state == 1 ? "Sprite/ui_public/win2_small_dikuang2_2green.png" : "Sprite/ui_public/win2_small_dikuang2_2.png");
            nonetxt.SetActive(state == 0);
            getBtn.gameObject.SetActive(state != 0);
            getBtn.isEnable = state != 2;
            ToolScriptExtend.SetGameObjectGrey(getBtn.transform, state == 2);

            getBtn.onClick.RemoveAllListeners();
            getBtn.onClick.AddListener(() =>
            {
                GameEntry.LogicData.DungeonData.DungeonClaimBox(showIndex, () =>
                {
                    if (!getList.Contains(showIndex)) getList.Add(showIndex);
                    OnUpdateInfo(true);
                });
            });

            var list = data.reward;
            tableViewH.GetItemCount = () => list.Count;
            tableViewH.GetItemGo = () => rewardItem;
            tableViewH.UpdateItemCell = (idx, item) =>
            {
                var itemObj = item.transform.Find("itemObj").gameObject;
                var itemModule = itemObj.GetComponent<UIItemModule>();
                var itemInfo = list[idx];
                itemModule.SetData(itemInfo.item_id, itemInfo.num);
                itemModule.DisplayInfo();
                itemModule.SetScale(0.55f);
            };
            if (tableViewH.itemPrototype) { tableViewH.ReloadData(); }
            else { tableViewH.InitTableViewByIndex(0); }
        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        private void OnBtnClaimClick()
        {
            GameEntry.LogicData.DungeonData.DungeonClaimBox(0, () =>
            {
                getList.Clear();
                for (int i = minBoxId + 1; i < nextIndex; i++)
                {
                    getList.Add(i);
                }
                OnUpdateInfo(true);
            });
        }
    }
}
