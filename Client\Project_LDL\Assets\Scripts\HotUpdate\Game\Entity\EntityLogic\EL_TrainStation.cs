using GameFramework.Event;
using UnityEngine;

namespace Game.Hotfix
{
    public class ED_TrainStation : ED_Building
    {
        public ED_TrainStation(int entityId) : base(entityId)
        {
        }
    }

    public class EL_TrainStation : EL_Building
    {
        private bool m_HasUnlock = false;
        private int m_TrainEntityId = 0;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
        }

        private void InitializeTrain(object param)
        {
            // 获取建筑外观显示实体
            if (eIdDisplay == null)
            {
                // 如果外观还没加载完成，再延迟一点
                Timers.Instance.Add("InitTrain_" + Id, 0.1f, InitializeTrain);
                return;
            }

            Entity displayEntity = GameEntry.Entity.GetGameEntity(eIdDisplay.Value);
            if (displayEntity == null)
            {
                ColorLog.Red("火车站外观显示实体未找到");
                return;
            }

            Transform container = displayEntity.transform;
            Transform start_point = container.Find("start_point");
            Transform stay_point = container.Find("stay_point");
            Transform end_point = container.Find("end_point");

            if (stay_point == null)
            {
                ColorLog.Red("火车站 stay_point 节点未找到");
                return;
            }

            // 创建火车实体
            m_TrainEntityId = GameEntry.Entity.ShowTrain();
            Entity train = GameEntry.Entity.GetGameEntity(m_TrainEntityId);
            if (train != null)
            {
                train.transform.SetParent(container);
                train.transform.position = stay_point.position;
                ColorLog.Green("火车初始化成功");
            }
        }

        protected override void OnShow(object userData)
        {
            base.OnShow(userData);
            m_HasUnlock = false;

            GameEntry.HUD.ShowHUD(this, EnumHUD.HUDTrainContract);

            CheckAndUnlockFogArea();

            GameEntry.Event.Subscribe(OnPvePathTriggeredEventArgs.EventId, OnPvePathTriggered);

            // 延迟初始化火车，等待建筑外观显示加载完成
            Timers.Instance.Add("InitTrain_" + Id, 0.1f, InitializeTrain);
        }

        private void OnPvePathTriggered(object sender, GameEventArgs e)
        {
            CheckAndUnlockFogArea();
        }

        protected override void OnHide(bool isShutdown, object userData)
        {
            base.OnHide(isShutdown, userData);

            GameEntry.Event.Unsubscribe(OnPvePathTriggeredEventArgs.EventId, OnPvePathTriggered);

            // 清理定时器
            Timers.Instance.Remove("InitTrain_" + Id);

            // 清理火车实体
            if (m_TrainEntityId != 0)
            {
                GameEntry.Entity.HideEntity(m_TrainEntityId);
                m_TrainEntityId = 0;
            }
        }

        /// <summary>
        /// 检查并解锁特定关卡的迷雾区域
        /// </summary>
        public void CheckAndUnlockFogArea()
        {
            if (m_HasUnlock) return;

            var pvePathData = GameEntry.LogicData.PvePathData;
            // 解锁车站月台
            if (pvePathData.CurStep >= 45)
            {
                m_HasUnlock = true;
                UnlockFogAreaForDungeon(pvePathData.CurStep, new Vector3(44, 0, 64), new Vector3(10, 0, 52));
            }
        }

        /// <summary>
        /// 为指定关卡解锁迷雾区域
        /// </summary>
        /// <param name="curStep">关卡ID</param>
        /// <param name="center">迷雾区域中心点</param>
        /// <param name="size">迷雾区域大小</param>
        private void UnlockFogAreaForDungeon(int curStep, Vector3 center, Vector3 size)
        {
            // 创建新的迷雾区域
            Bounds newFogBounds = new(center, size);
            // 添加到迷雾系统
            GameEntry.CityMap.mainCityFog.AddBound(newFogBounds);
            // 输出日志
            ColorLog.Green($"小人到达{curStep}关位置，已添加新的迷雾区域: 中心{center}, 大小{size}");
        }

        public override void OnClick()
        {
            base.OnClick();
            GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTruckForm);
        }
    }
}