using GameFramework.Event;
using UnityEngine;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public class ED_TrainStation : ED_Building
    {
        public ED_TrainStation(int entityId) : base(entityId)
        {
        }
    }

    public class EL_TrainStation : EL_Building
    {
        private bool m_HasUnlock = false;
        private int m_TrainEntityId = 0;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
        }

        private Transform m_TrainContainer;
        private Transform m_StayPoint;

        private void InitializeTrain(Transform container)
        {
            if (container == null)
            {
                ColorLog.Red("火车站容器为空");
                return;
            }

            Transform start_point = container.Find("start_point");
            Transform stay_point = container.Find("stay_point");
            Transform end_point = container.Find("end_point");

            if (stay_point == null)
            {
                ColorLog.Red("火车站 stay_point 节点未找到");
                return;
            }

            if (start_point == null)
            {
                ColorLog.Red("火车站 start_point 节点未找到");
                return;
            }

            if (end_point == null)
            {
                ColorLog.Red("火车站 end_point 节点未找到");
                return;
            }

            // 保存容器和位置信息
            m_TrainContainer = container;
            m_StayPoint = stay_point;

            // 订阅实体显示成功事件
            GameEntry.Event.Subscribe(ShowEntitySuccessEventArgs.EventId, OnShowEntitySuccess);

            // 创建火车实体
            m_TrainEntityId = GameEntry.Entity.ShowTrain();
            ColorLog.Green($"开始创建火车实体，ID: {m_TrainEntityId}");
        }

        private void OnShowEntitySuccess(object sender, GameEventArgs e)
        {
            ShowEntitySuccessEventArgs args = (ShowEntitySuccessEventArgs)e;

            // 检查是否是我们的火车实体
            if (args.Entity.Id == m_TrainEntityId)
            {
                // 取消订阅事件
                GameEntry.Event.Unsubscribe(ShowEntitySuccessEventArgs.EventId, OnShowEntitySuccess);

                // 将火车附加到火车站实体下
                Entity train = GameEntry.Entity.GetGameEntity(m_TrainEntityId);
                if (train != null && m_TrainContainer != null && m_StayPoint != null)
                {
                    // 使用 AttachEntity 将火车附加到火车站
                    GameEntry.Entity.AttachEntity(train.Entity, Entity, m_TrainContainer);

                    // 设置火车的本地位置到停靠点
                    train.transform.localPosition = m_TrainContainer.InverseTransformPoint(m_StayPoint.position);

                    // 初始化火车的站点信息
                    if (train is EL_Train trainLogic)
                    {
                        Transform start_point = m_TrainContainer.Find("start_point");
                        Transform end_point = m_TrainContainer.Find("end_point");
                        trainLogic.InitializeStationPoints(start_point, m_StayPoint, end_point);
                    }

                    ColorLog.Green("火车附加到火车站成功");
                }
                else
                {
                    ColorLog.Red("火车实体或容器信息丢失");
                }
            }
        }

        protected override void OnShow(object userData)
        {
            base.OnShow(userData);
            m_HasUnlock = false;

            GameEntry.HUD.ShowHUD(this, EnumHUD.HUDTrainContract);

            CheckAndUnlockFogArea();

            GameEntry.Event.Subscribe(OnPvePathTriggeredEventArgs.EventId, OnPvePathTriggered);
        }

        protected override void OnAttached(EntityLogic childEntity, Transform parentTransform, object userData)
        {
            base.OnAttached(childEntity, parentTransform, userData);

            // 当建筑外观显示实体附加时，初始化火车
            if (childEntity is EL_BuildingDisplay)
            {
                InitializeTrain(childEntity.CachedTransform);
            }
        }

        private void OnPvePathTriggered(object sender, GameEventArgs e)
        {
            CheckAndUnlockFogArea();
        }

        protected override void OnHide(bool isShutdown, object userData)
        {
            base.OnHide(isShutdown, userData);

            GameEntry.Event.Unsubscribe(OnPvePathTriggeredEventArgs.EventId, OnPvePathTriggered);
            GameEntry.Event.Unsubscribe(ShowEntitySuccessEventArgs.EventId, OnShowEntitySuccess);

            // 清理引用（火车实体会自动跟随父实体隐藏，无需手动处理）
            m_TrainEntityId = 0;
            m_TrainContainer = null;
            m_StayPoint = null;
        }

        /// <summary>
        /// 检查并解锁特定关卡的迷雾区域
        /// </summary>
        public void CheckAndUnlockFogArea()
        {
            if (m_HasUnlock) return;

            var pvePathData = GameEntry.LogicData.PvePathData;
            // 解锁车站月台
            if (pvePathData.CurStep >= 45)
            {
                m_HasUnlock = true;
                UnlockFogAreaForDungeon(pvePathData.CurStep, new Vector3(44, 0, 64), new Vector3(10, 0, 52));
            }
        }

        /// <summary>
        /// 为指定关卡解锁迷雾区域
        /// </summary>
        /// <param name="curStep">关卡ID</param>
        /// <param name="center">迷雾区域中心点</param>
        /// <param name="size">迷雾区域大小</param>
        private void UnlockFogAreaForDungeon(int curStep, Vector3 center, Vector3 size)
        {
            // 创建新的迷雾区域
            Bounds newFogBounds = new(center, size);
            // 添加到迷雾系统
            GameEntry.CityMap.mainCityFog.AddBound(newFogBounds);
            // 输出日志
            ColorLog.Green($"小人到达{curStep}关位置，已添加新的迷雾区域: 中心{center}, 大小{size}");
        }

        /// <summary>
        /// 播放火车进站动画
        /// </summary>
        /// <param name="duration">动画时长</param>
        /// <param name="onComplete">完成回调</param>
        public void PlayTrainEnterAnimation(float duration = 2f, System.Action onComplete = null)
        {
            if (m_TrainEntityId != 0)
            {
                Entity train = GameEntry.Entity.GetGameEntity(m_TrainEntityId);
                if (train is EL_Train trainLogic)
                {
                    trainLogic.PlayEnterStationAnimation(duration, onComplete);
                }
                else
                {
                    ColorLog.Red("火车实体未找到或类型错误");
                    onComplete?.Invoke();
                }
            }
            else
            {
                ColorLog.Red("火车未初始化");
                onComplete?.Invoke();
            }
        }

        /// <summary>
        /// 播放火车出站动画
        /// </summary>
        /// <param name="duration">动画时长</param>
        /// <param name="onComplete">完成回调</param>
        public void PlayTrainExitAnimation(float duration = 2f, System.Action onComplete = null)
        {
            if (m_TrainEntityId != 0)
            {
                Entity train = GameEntry.Entity.GetGameEntity(m_TrainEntityId);
                if (train is EL_Train trainLogic)
                {
                    trainLogic.PlayExitStationAnimation(duration, onComplete);
                }
                else
                {
                    ColorLog.Red("火车实体未找到或类型错误");
                    onComplete?.Invoke();
                }
            }
            else
            {
                ColorLog.Red("火车未初始化");
                onComplete?.Invoke();
            }
        }

        /// <summary>
        /// 停止火车动画
        /// </summary>
        public void StopTrainAnimation()
        {
            if (m_TrainEntityId != 0)
            {
                Entity train = GameEntry.Entity.GetGameEntity(m_TrainEntityId);
                if (train is EL_Train trainLogic)
                {
                    trainLogic.StopCurrentAnimation();
                }
            }
        }

        /// <summary>
        /// 让火车瞬移到指定站点
        /// </summary>
        /// <param name="stationType">站点类型</param>
        public void TeleportTrainToStation(TrainStationType stationType)
        {
            if (m_TrainEntityId != 0)
            {
                Entity train = GameEntry.Entity.GetGameEntity(m_TrainEntityId);
                if (train is EL_Train trainLogic)
                {
                    trainLogic.TeleportToStation(stationType);
                }
            }
        }

        public override void OnClick()
        {
            base.OnClick();
            GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTruckForm);
        }
    }
}