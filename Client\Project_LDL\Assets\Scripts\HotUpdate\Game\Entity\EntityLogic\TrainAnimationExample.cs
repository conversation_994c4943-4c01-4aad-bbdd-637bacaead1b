using UnityEngine;

namespace Game.Hotfix
{
    /// <summary>
    /// 火车动画使用示例
    /// 这个类展示了如何使用火车站的动画接口
    /// </summary>
    public class TrainAnimationExample : MonoBehaviour
    {
        [Header("火车站引用")]
        public EL_TrainStation trainStation;
        
        [Header("动画参数")]
        public float enterDuration = 2f;
        public float exitDuration = 2f;
        public float stayDuration = 3f;

        private void Start()
        {
            // 示例：完整的火车进站-停留-出站流程
            if (trainStation != null)
            {
                StartTrainSequence();
            }
        }

        /// <summary>
        /// 开始完整的火车运行序列
        /// </summary>
        public void StartTrainSequence()
        {
            ColorLog.Green("开始火车运行序列");
            
            // 第一步：火车进站
            trainStation.PlayTrainEnterAnimation(enterDuration, OnTrainEntered);
        }

        /// <summary>
        /// 火车进站完成回调
        /// </summary>
        private void OnTrainEntered()
        {
            ColorLog.Green("火车已进站，开始停留");
            
            // 第二步：停留一段时间
            Invoke(nameof(StartTrainExit), stayDuration);
        }

        /// <summary>
        /// 开始火车出站
        /// </summary>
        private void StartTrainExit()
        {
            ColorLog.Green("火车开始出站");
            
            // 第三步：火车出站
            trainStation.PlayTrainExitAnimation(exitDuration, OnTrainExited);
        }

        /// <summary>
        /// 火车出站完成回调
        /// </summary>
        private void OnTrainExited()
        {
            ColorLog.Green("火车已出站，序列完成");
            
            // 可以在这里重新开始循环，或者执行其他逻辑
            // Invoke(nameof(RestartSequence), 5f);
        }

        /// <summary>
        /// 重新开始序列（可选）
        /// </summary>
        private void RestartSequence()
        {
            // 让火车瞬移回起始点，然后重新开始
            trainStation.TeleportTrainToStation(TrainStationType.Start);
            StartTrainSequence();
        }

        #region 测试按钮方法（可以在Inspector中调用）

        [ContextMenu("测试进站动画")]
        public void TestEnterAnimation()
        {
            if (trainStation != null)
            {
                trainStation.PlayTrainEnterAnimation(enterDuration);
            }
        }

        [ContextMenu("测试出站动画")]
        public void TestExitAnimation()
        {
            if (trainStation != null)
            {
                trainStation.PlayTrainExitAnimation(exitDuration);
            }
        }

        [ContextMenu("停止动画")]
        public void TestStopAnimation()
        {
            if (trainStation != null)
            {
                trainStation.StopTrainAnimation();
            }
        }

        [ContextMenu("瞬移到起始点")]
        public void TestTeleportToStart()
        {
            if (trainStation != null)
            {
                trainStation.TeleportTrainToStation(TrainStationType.Start);
            }
        }

        [ContextMenu("瞬移到停靠点")]
        public void TestTeleportToStay()
        {
            if (trainStation != null)
            {
                trainStation.TeleportTrainToStation(TrainStationType.Stay);
            }
        }

        [ContextMenu("瞬移到终点")]
        public void TestTeleportToEnd()
        {
            if (trainStation != null)
            {
                trainStation.TeleportTrainToStation(TrainStationType.End);
            }
        }

        #endregion
    }
}
