# 火车动画系统使用指南

## 概述

火车动画系统提供了完整的火车进站、出站动画控制，支持事件驱动的UI交互。

## 核心组件

### 1. EL_Train (火车实体)
负责具体的动画播放和位置控制。

### 2. EL_TrainStation (火车站实体)
管理火车实体，提供对外接口和事件触发。

### 3. UITrainControlForm (UI控制界面)
示例UI界面，展示如何通过事件与火车动画交互。

## 主要功能

### 🚂 动画类型

1. **进站动画** - 从起始点到停靠点的三段式动画
2. **出站动画** - 从停靠点到终点的三段式动画
3. **瞬移功能** - 直接移动到指定站点
4. **停止动画** - 中断当前播放的动画

### 🎮 调用接口

#### 直接调用方式
```csharp
// 获取火车站实体
EL_TrainStation trainStation = GetTrainStation();

// 播放进站动画
trainStation.PlayTrainEnterAnimation(2f, () => {
    ColorLog.Green("进站完成");
});

// 播放出站动画
trainStation.PlayTrainExitAnimation(2f, () => {
    ColorLog.Green("出站完成");
});

// 停止动画
trainStation.StopTrainAnimation();

// 瞬移到指定站点
trainStation.TeleportTrainToStation(TrainStationType.Start);
```

#### 事件驱动方式
```csharp
// 订阅事件
EL_TrainStation.OnTrainAnimationStarted += (stationId, animationType) => {
    ColorLog.Green($"动画开始: {animationType}");
};

EL_TrainStation.OnTrainAnimationCompleted += (stationId, animationType) => {
    ColorLog.Green($"动画完成: {animationType}");
};

EL_TrainStation.OnTrainAnimationStopped += (stationId, animationType) => {
    ColorLog.Yellow($"动画停止: {animationType}");
};
```

### 🎯 完整序列示例

```csharp
public void PlayCompleteTrainSequence()
{
    EL_TrainStation trainStation = GetTrainStation();
    
    // 1. 瞬移到起始点
    trainStation.TeleportTrainToStation(TrainStationType.Start);
    
    // 2. 延迟后开始进站
    Timers.Instance.Add("TrainEnter", 0.5f, (param) => {
        trainStation.PlayTrainEnterAnimation(2f, () => {
            // 3. 进站完成，停留3秒
            Timers.Instance.Add("TrainStay", 3f, (param2) => {
                // 4. 开始出站
                trainStation.PlayTrainExitAnimation(2f, () => {
                    ColorLog.Green("完整序列结束");
                });
            });
        });
    });
}
```

## 事件系统

### 事件类型

1. **OnTrainAnimationStarted** - 动画开始
2. **OnTrainAnimationCompleted** - 动画完成  
3. **OnTrainAnimationStopped** - 动画停止

### 事件参数

- `int trainStationId` - 火车站实体ID
- `string animationType` - 动画类型 ("Enter", "Exit", "Stop")

### UI界面集成

```csharp
public class MyUIForm : UIFormLogic
{
    protected override void OnOpen(object userData)
    {
        base.OnOpen(userData);
        
        // 订阅事件
        EL_TrainStation.OnTrainAnimationStarted += OnTrainAnimationStarted;
        EL_TrainStation.OnTrainAnimationCompleted += OnTrainAnimationCompleted;
    }
    
    protected override void OnClose(bool isShutdown, object userData)
    {
        base.OnClose(isShutdown, userData);
        
        // 取消订阅
        EL_TrainStation.OnTrainAnimationStarted -= OnTrainAnimationStarted;
        EL_TrainStation.OnTrainAnimationCompleted -= OnTrainAnimationCompleted;
    }
    
    private void OnTrainAnimationStarted(int stationId, string animationType)
    {
        // 更新UI状态
        UpdateAnimationStatus($"开始: {animationType}");
    }
    
    private void OnTrainAnimationCompleted(int stationId, string animationType)
    {
        // 更新UI状态
        UpdateAnimationStatus($"完成: {animationType}");
    }
}
```

## 动画参数

### 时长设置
- 默认进站时长: 2秒
- 默认出站时长: 2秒
- 可通过参数自定义

### 动画曲线
- **进站**: 缓慢启动 → 匀速行驶 → 减速进站
- **出站**: 缓慢启动 → 加速离开 → 高速驶离

## 注意事项

1. **实体生命周期**: 火车实体会自动跟随火车站的显示/隐藏
2. **动画冲突**: 新动画会自动停止之前的动画
3. **错误处理**: 所有接口都包含完善的空值检查和错误日志
4. **内存管理**: 实体隐藏时会自动清理所有动画和事件订阅

## 扩展建议

1. **音效集成**: 在动画事件中添加音效播放
2. **粒子效果**: 在关键节点添加烟雾、蒸汽等效果
3. **UI反馈**: 根据动画状态更新UI元素
4. **数据统计**: 记录火车运行次数、时长等数据

## 故障排除

### 常见问题

1. **火车不显示**: 检查火车站预制体是否包含正确的站点节点
2. **动画不播放**: 确认火车实体已正确初始化
3. **事件不触发**: 检查事件订阅是否在正确的生命周期中

### 调试日志

系统会输出详细的调试日志，包括：
- 火车实体创建状态
- 动画播放进度
- 事件触发情况
- 错误信息

通过 `ColorLog` 可以在控制台查看这些信息。
