// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: soldier.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Soldier {

  /// <summary>Holder for reflection information generated from soldier.proto</summary>
  public static partial class SoldierReflection {

    #region Descriptor
    /// <summary>File descriptor for soldier.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static SoldierReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Cg1zb2xkaWVyLnByb3RvEgdzb2xkaWVyGg1hcnRpY2xlLnByb3RvGgtidWls",
            "ZC5wcm90byIxCg1Tb2xkaWVyQ2hhbmdlEiAKCHNvbGRpZXJzGAEgAygLMg4u",
            "YnVpbGQuU29sZGllciK/AQoMU29sZGllck9wUmVxEg8KB2J1aWxkTm8YASAB",
            "KA0SHwoHc29sZGllchgCIAEoCzIOLmJ1aWxkLlNvbGRpZXISJgoGb3BUeXBl",
            "GAMgASgOMhYuc29sZGllci5Tb2xkaWVyT3BUeXBlEjEKD3NvbGRpZXJDb3N0",
            "VHlwZRgEIAEoDjIYLnNvbGRpZXIuU29sZGllckNvc3RUeXBlEiIKCGFydGlj",
            "bGVzGAUgAygLMhAuYXJ0aWNsZS5BcnRpY2xlIjgKDVNvbGRpZXJPcFJlc3AS",
            "JwoGcmVzdWx0GAEgASgLMhcuYnVpbGQuQnVpbGRRdWV1ZVJlc3VsdCKOAQoT",
            "U29sZGllclRyZWF0bWVudFJlcRIgCghzb2xkaWVycxgBIAMoCzIOLmJ1aWxk",
            "LlNvbGRpZXISMQoPc29sZGllckNvc3RUeXBlGAIgASgOMhguc29sZGllci5T",
            "b2xkaWVyQ29zdFR5cGUSIgoIYXJ0aWNsZXMYAyADKAsyEC5hcnRpY2xlLkFy",
            "dGljbGUiPwoUU29sZGllclRyZWF0bWVudFJlc3ASJwoGcmVzdWx0GAEgASgL",
            "MhcuYnVpbGQuQnVpbGRRdWV1ZVJlc3VsdCpfCg1Tb2xkaWVyT3BUeXBlEhUK",
            "EVNvbGRpZXJPcFR5cGVfTmlsEAASGgoWU29sZGllck9wVHlwZV9UcmFpbmlu",
            "ZxABEhsKF1NvbGRpZXJPcFR5cGVfUHJvbW90aW9uEAIqjAEKD1NvbGRpZXJD",
            "b3N0VHlwZRIXChNTb2xkaWVyQ29zdFR5cGVfTmlsEAASGgoWU29sZGllckNv",
            "c3RUeXBlX0NvbW1vbhABEiAKHFNvbGRpZXJDb3N0VHlwZV9SZXNvdXJjZXNO",
            "b3QQAhIiCh5Tb2xkaWVyQ29zdFR5cGVfQWNjZWxlcmF0ZUl0ZW0QA0IaWhhz",
            "ZXJ2ZXIvYXBpL3BiL3BiX3NvbGRpZXJiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Article.ArticleReflection.Descriptor, global::Build.BuildReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Soldier.SoldierOpType), typeof(global::Soldier.SoldierCostType), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Soldier.SoldierChange), global::Soldier.SoldierChange.Parser, new[]{ "Soldiers" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Soldier.SoldierOpReq), global::Soldier.SoldierOpReq.Parser, new[]{ "BuildNo", "Soldier", "OpType", "SoldierCostType", "Articles" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Soldier.SoldierOpResp), global::Soldier.SoldierOpResp.Parser, new[]{ "Result" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Soldier.SoldierTreatmentReq), global::Soldier.SoldierTreatmentReq.Parser, new[]{ "Soldiers", "SoldierCostType", "Articles" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Soldier.SoldierTreatmentResp), global::Soldier.SoldierTreatmentResp.Parser, new[]{ "Result" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  public enum SoldierOpType {
    /// <summary>
    /// 占位
    /// </summary>
    [pbr::OriginalName("SoldierOpType_Nil")] Nil = 0,
    /// <summary>
    ///训练士兵
    /// </summary>
    [pbr::OriginalName("SoldierOpType_Training")] Training = 1,
    /// <summary>
    ///晋级士兵
    /// </summary>
    [pbr::OriginalName("SoldierOpType_Promotion")] Promotion = 2,
  }

  public enum SoldierCostType {
    /// <summary>
    /// 占位
    /// </summary>
    [pbr::OriginalName("SoldierCostType_Nil")] Nil = 0,
    /// <summary>
    ///普通消耗
    /// </summary>
    [pbr::OriginalName("SoldierCostType_Common")] Common = 1,
    /// <summary>
    ///资源不足 使用钻石购买资源并且购买时间,多余的资源加回玩家仓库
    /// </summary>
    [pbr::OriginalName("SoldierCostType_ResourcesNot")] ResourcesNot = 2,
    /// <summary>
    ///资源足够 使用加速道具,剩余时间使用钻石购买
    /// </summary>
    [pbr::OriginalName("SoldierCostType_AccelerateItem")] AccelerateItem = 3,
  }

  #endregion

  #region Messages
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SoldierChange : pb::IMessage<SoldierChange>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SoldierChange> _parser = new pb::MessageParser<SoldierChange>(() => new SoldierChange());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SoldierChange> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Soldier.SoldierReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SoldierChange() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SoldierChange(SoldierChange other) : this() {
      soldiers_ = other.soldiers_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SoldierChange Clone() {
      return new SoldierChange(this);
    }

    /// <summary>Field number for the "soldiers" field.</summary>
    public const int SoldiersFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Build.Soldier> _repeated_soldiers_codec
        = pb::FieldCodec.ForMessage(10, global::Build.Soldier.Parser);
    private readonly pbc::RepeatedField<global::Build.Soldier> soldiers_ = new pbc::RepeatedField<global::Build.Soldier>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Build.Soldier> Soldiers {
      get { return soldiers_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SoldierChange);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SoldierChange other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!soldiers_.Equals(other.soldiers_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= soldiers_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      soldiers_.WriteTo(output, _repeated_soldiers_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      soldiers_.WriteTo(ref output, _repeated_soldiers_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += soldiers_.CalculateSize(_repeated_soldiers_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SoldierChange other) {
      if (other == null) {
        return;
      }
      soldiers_.Add(other.soldiers_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            soldiers_.AddEntriesFrom(input, _repeated_soldiers_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            soldiers_.AddEntriesFrom(ref input, _repeated_soldiers_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 请求操作士兵(训练,晋级)
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SoldierOpReq : pb::IMessage<SoldierOpReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SoldierOpReq> _parser = new pb::MessageParser<SoldierOpReq>(() => new SoldierOpReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SoldierOpReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Soldier.SoldierReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SoldierOpReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SoldierOpReq(SoldierOpReq other) : this() {
      buildNo_ = other.buildNo_;
      soldier_ = other.soldier_ != null ? other.soldier_.Clone() : null;
      opType_ = other.opType_;
      soldierCostType_ = other.soldierCostType_;
      articles_ = other.articles_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SoldierOpReq Clone() {
      return new SoldierOpReq(this);
    }

    /// <summary>Field number for the "buildNo" field.</summary>
    public const int BuildNoFieldNumber = 1;
    private uint buildNo_;
    /// <summary>
    ///建筑No
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint BuildNo {
      get { return buildNo_; }
      set {
        buildNo_ = value;
      }
    }

    /// <summary>Field number for the "soldier" field.</summary>
    public const int SoldierFieldNumber = 2;
    private global::Build.Soldier soldier_;
    /// <summary>
    ///士兵信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Build.Soldier Soldier {
      get { return soldier_; }
      set {
        soldier_ = value;
      }
    }

    /// <summary>Field number for the "opType" field.</summary>
    public const int OpTypeFieldNumber = 3;
    private global::Soldier.SoldierOpType opType_ = global::Soldier.SoldierOpType.Nil;
    /// <summary>
    ///操作类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Soldier.SoldierOpType OpType {
      get { return opType_; }
      set {
        opType_ = value;
      }
    }

    /// <summary>Field number for the "soldierCostType" field.</summary>
    public const int SoldierCostTypeFieldNumber = 4;
    private global::Soldier.SoldierCostType soldierCostType_ = global::Soldier.SoldierCostType.Nil;
    /// <summary>
    /// 消耗类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Soldier.SoldierCostType SoldierCostType {
      get { return soldierCostType_; }
      set {
        soldierCostType_ = value;
      }
    }

    /// <summary>Field number for the "articles" field.</summary>
    public const int ArticlesFieldNumber = 5;
    private static readonly pb::FieldCodec<global::Article.Article> _repeated_articles_codec
        = pb::FieldCodec.ForMessage(42, global::Article.Article.Parser);
    private readonly pbc::RepeatedField<global::Article.Article> articles_ = new pbc::RepeatedField<global::Article.Article>();
    /// <summary>
    ///加速物品
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Article.Article> Articles {
      get { return articles_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SoldierOpReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SoldierOpReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (BuildNo != other.BuildNo) return false;
      if (!object.Equals(Soldier, other.Soldier)) return false;
      if (OpType != other.OpType) return false;
      if (SoldierCostType != other.SoldierCostType) return false;
      if(!articles_.Equals(other.articles_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (BuildNo != 0) hash ^= BuildNo.GetHashCode();
      if (soldier_ != null) hash ^= Soldier.GetHashCode();
      if (OpType != global::Soldier.SoldierOpType.Nil) hash ^= OpType.GetHashCode();
      if (SoldierCostType != global::Soldier.SoldierCostType.Nil) hash ^= SoldierCostType.GetHashCode();
      hash ^= articles_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (BuildNo != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(BuildNo);
      }
      if (soldier_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Soldier);
      }
      if (OpType != global::Soldier.SoldierOpType.Nil) {
        output.WriteRawTag(24);
        output.WriteEnum((int) OpType);
      }
      if (SoldierCostType != global::Soldier.SoldierCostType.Nil) {
        output.WriteRawTag(32);
        output.WriteEnum((int) SoldierCostType);
      }
      articles_.WriteTo(output, _repeated_articles_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (BuildNo != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(BuildNo);
      }
      if (soldier_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Soldier);
      }
      if (OpType != global::Soldier.SoldierOpType.Nil) {
        output.WriteRawTag(24);
        output.WriteEnum((int) OpType);
      }
      if (SoldierCostType != global::Soldier.SoldierCostType.Nil) {
        output.WriteRawTag(32);
        output.WriteEnum((int) SoldierCostType);
      }
      articles_.WriteTo(ref output, _repeated_articles_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (BuildNo != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(BuildNo);
      }
      if (soldier_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Soldier);
      }
      if (OpType != global::Soldier.SoldierOpType.Nil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) OpType);
      }
      if (SoldierCostType != global::Soldier.SoldierCostType.Nil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) SoldierCostType);
      }
      size += articles_.CalculateSize(_repeated_articles_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SoldierOpReq other) {
      if (other == null) {
        return;
      }
      if (other.BuildNo != 0) {
        BuildNo = other.BuildNo;
      }
      if (other.soldier_ != null) {
        if (soldier_ == null) {
          Soldier = new global::Build.Soldier();
        }
        Soldier.MergeFrom(other.Soldier);
      }
      if (other.OpType != global::Soldier.SoldierOpType.Nil) {
        OpType = other.OpType;
      }
      if (other.SoldierCostType != global::Soldier.SoldierCostType.Nil) {
        SoldierCostType = other.SoldierCostType;
      }
      articles_.Add(other.articles_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            BuildNo = input.ReadUInt32();
            break;
          }
          case 18: {
            if (soldier_ == null) {
              Soldier = new global::Build.Soldier();
            }
            input.ReadMessage(Soldier);
            break;
          }
          case 24: {
            OpType = (global::Soldier.SoldierOpType) input.ReadEnum();
            break;
          }
          case 32: {
            SoldierCostType = (global::Soldier.SoldierCostType) input.ReadEnum();
            break;
          }
          case 42: {
            articles_.AddEntriesFrom(input, _repeated_articles_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            BuildNo = input.ReadUInt32();
            break;
          }
          case 18: {
            if (soldier_ == null) {
              Soldier = new global::Build.Soldier();
            }
            input.ReadMessage(Soldier);
            break;
          }
          case 24: {
            OpType = (global::Soldier.SoldierOpType) input.ReadEnum();
            break;
          }
          case 32: {
            SoldierCostType = (global::Soldier.SoldierCostType) input.ReadEnum();
            break;
          }
          case 42: {
            articles_.AddEntriesFrom(ref input, _repeated_articles_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 响应操作士兵(训练,晋级)
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SoldierOpResp : pb::IMessage<SoldierOpResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SoldierOpResp> _parser = new pb::MessageParser<SoldierOpResp>(() => new SoldierOpResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SoldierOpResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Soldier.SoldierReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SoldierOpResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SoldierOpResp(SoldierOpResp other) : this() {
      result_ = other.result_ != null ? other.result_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SoldierOpResp Clone() {
      return new SoldierOpResp(this);
    }

    /// <summary>Field number for the "result" field.</summary>
    public const int ResultFieldNumber = 1;
    private global::Build.BuildQueueResult result_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Build.BuildQueueResult Result {
      get { return result_; }
      set {
        result_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SoldierOpResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SoldierOpResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(Result, other.Result)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (result_ != null) hash ^= Result.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (result_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Result);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (result_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Result);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (result_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Result);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SoldierOpResp other) {
      if (other == null) {
        return;
      }
      if (other.result_ != null) {
        if (result_ == null) {
          Result = new global::Build.BuildQueueResult();
        }
        Result.MergeFrom(other.Result);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (result_ == null) {
              Result = new global::Build.BuildQueueResult();
            }
            input.ReadMessage(Result);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (result_ == null) {
              Result = new global::Build.BuildQueueResult();
            }
            input.ReadMessage(Result);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 请求治疗士兵
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SoldierTreatmentReq : pb::IMessage<SoldierTreatmentReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SoldierTreatmentReq> _parser = new pb::MessageParser<SoldierTreatmentReq>(() => new SoldierTreatmentReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SoldierTreatmentReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Soldier.SoldierReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SoldierTreatmentReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SoldierTreatmentReq(SoldierTreatmentReq other) : this() {
      soldiers_ = other.soldiers_.Clone();
      soldierCostType_ = other.soldierCostType_;
      articles_ = other.articles_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SoldierTreatmentReq Clone() {
      return new SoldierTreatmentReq(this);
    }

    /// <summary>Field number for the "soldiers" field.</summary>
    public const int SoldiersFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Build.Soldier> _repeated_soldiers_codec
        = pb::FieldCodec.ForMessage(10, global::Build.Soldier.Parser);
    private readonly pbc::RepeatedField<global::Build.Soldier> soldiers_ = new pbc::RepeatedField<global::Build.Soldier>();
    /// <summary>
    ///士兵信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Build.Soldier> Soldiers {
      get { return soldiers_; }
    }

    /// <summary>Field number for the "soldierCostType" field.</summary>
    public const int SoldierCostTypeFieldNumber = 2;
    private global::Soldier.SoldierCostType soldierCostType_ = global::Soldier.SoldierCostType.Nil;
    /// <summary>
    /// 消耗类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Soldier.SoldierCostType SoldierCostType {
      get { return soldierCostType_; }
      set {
        soldierCostType_ = value;
      }
    }

    /// <summary>Field number for the "articles" field.</summary>
    public const int ArticlesFieldNumber = 3;
    private static readonly pb::FieldCodec<global::Article.Article> _repeated_articles_codec
        = pb::FieldCodec.ForMessage(26, global::Article.Article.Parser);
    private readonly pbc::RepeatedField<global::Article.Article> articles_ = new pbc::RepeatedField<global::Article.Article>();
    /// <summary>
    ///加速物品
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Article.Article> Articles {
      get { return articles_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SoldierTreatmentReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SoldierTreatmentReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!soldiers_.Equals(other.soldiers_)) return false;
      if (SoldierCostType != other.SoldierCostType) return false;
      if(!articles_.Equals(other.articles_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= soldiers_.GetHashCode();
      if (SoldierCostType != global::Soldier.SoldierCostType.Nil) hash ^= SoldierCostType.GetHashCode();
      hash ^= articles_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      soldiers_.WriteTo(output, _repeated_soldiers_codec);
      if (SoldierCostType != global::Soldier.SoldierCostType.Nil) {
        output.WriteRawTag(16);
        output.WriteEnum((int) SoldierCostType);
      }
      articles_.WriteTo(output, _repeated_articles_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      soldiers_.WriteTo(ref output, _repeated_soldiers_codec);
      if (SoldierCostType != global::Soldier.SoldierCostType.Nil) {
        output.WriteRawTag(16);
        output.WriteEnum((int) SoldierCostType);
      }
      articles_.WriteTo(ref output, _repeated_articles_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += soldiers_.CalculateSize(_repeated_soldiers_codec);
      if (SoldierCostType != global::Soldier.SoldierCostType.Nil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) SoldierCostType);
      }
      size += articles_.CalculateSize(_repeated_articles_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SoldierTreatmentReq other) {
      if (other == null) {
        return;
      }
      soldiers_.Add(other.soldiers_);
      if (other.SoldierCostType != global::Soldier.SoldierCostType.Nil) {
        SoldierCostType = other.SoldierCostType;
      }
      articles_.Add(other.articles_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            soldiers_.AddEntriesFrom(input, _repeated_soldiers_codec);
            break;
          }
          case 16: {
            SoldierCostType = (global::Soldier.SoldierCostType) input.ReadEnum();
            break;
          }
          case 26: {
            articles_.AddEntriesFrom(input, _repeated_articles_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            soldiers_.AddEntriesFrom(ref input, _repeated_soldiers_codec);
            break;
          }
          case 16: {
            SoldierCostType = (global::Soldier.SoldierCostType) input.ReadEnum();
            break;
          }
          case 26: {
            articles_.AddEntriesFrom(ref input, _repeated_articles_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 响应治疗士兵
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SoldierTreatmentResp : pb::IMessage<SoldierTreatmentResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SoldierTreatmentResp> _parser = new pb::MessageParser<SoldierTreatmentResp>(() => new SoldierTreatmentResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SoldierTreatmentResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Soldier.SoldierReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SoldierTreatmentResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SoldierTreatmentResp(SoldierTreatmentResp other) : this() {
      result_ = other.result_ != null ? other.result_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SoldierTreatmentResp Clone() {
      return new SoldierTreatmentResp(this);
    }

    /// <summary>Field number for the "result" field.</summary>
    public const int ResultFieldNumber = 1;
    private global::Build.BuildQueueResult result_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Build.BuildQueueResult Result {
      get { return result_; }
      set {
        result_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SoldierTreatmentResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SoldierTreatmentResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(Result, other.Result)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (result_ != null) hash ^= Result.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (result_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Result);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (result_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Result);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (result_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Result);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SoldierTreatmentResp other) {
      if (other == null) {
        return;
      }
      if (other.result_ != null) {
        if (result_ == null) {
          Result = new global::Build.BuildQueueResult();
        }
        Result.MergeFrom(other.Result);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (result_ == null) {
              Result = new global::Build.BuildQueueResult();
            }
            input.ReadMessage(Result);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (result_ == null) {
              Result = new global::Build.BuildQueueResult();
            }
            input.ReadMessage(Result);
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
