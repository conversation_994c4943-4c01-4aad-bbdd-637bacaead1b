%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &131986640935666217
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3420938637793754425}
  m_Layer: 9
  m_Name: Bone002
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3420938637793754425
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 131986640935666217}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000059604645, y: -9.7144515e-15, z: -0.00000016298145, w: 1}
  m_LocalPosition: {x: -1.1777667, y: -0.00000047683716, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2137327177826013742}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &976450055812484119
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 488320096610899629}
  - component: {fileID: 1156836456091308547}
  m_Layer: 9
  m_Name: 23_tank_battle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &488320096610899629
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 976450055812484119}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2683634742000552013}
  - {fileID: 7234580753912142094}
  - {fileID: 9086936544123485604}
  m_Father: {fileID: 2623476371579210795}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &1156836456091308547
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 976450055812484119}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: a94d2ef55225b1748b085b07dcff0437, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &1346506174107608150
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3735258799252874561}
  m_Layer: 9
  m_Name: Dummy003
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3735258799252874561
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1346506174107608150}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0051272167, y: -0.005170258, z: 0.7097836, w: 0.7043822}
  m_LocalPosition: {x: -1.5051877, y: -0.007883148, z: -0.03798538}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3131362217246585584}
  m_Father: {fileID: 2137327177826013742}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1723148635434123270
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2623476371579210795}
  - component: {fileID: 1802021318263508581}
  - component: {fileID: 6987037537855483752}
  m_Layer: 9
  m_Name: role_12503_battle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2623476371579210795
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1723148635434123270}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 488320096610899629}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1802021318263508581
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1723148635434123270}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f1770034b84515b45a96b3f473aae6c1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ObjTransform: {fileID: 3131362217246585584}
--- !u!114 &6987037537855483752
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1723148635434123270}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d1eb6e16bbb69d1478b3a81466b2d544, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  slots:
  - Slot: 0
    Transform: {fileID: 4889907820478419166}
  - Slot: 1
    Transform: {fileID: 9140229367415456083}
  - Slot: 2
    Transform: {fileID: 2218561450341055425}
  - Slot: 9
    Transform: {fileID: 2683634742000552013}
--- !u!1 &2348468361928561399
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5995681858760206994}
  m_Layer: 9
  m_Name: Bone008
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5995681858760206994
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2348468361928561399}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000023841858, y: 3.162489e-14, z: 1, w: -0.00000013315805}
  m_LocalPosition: {x: 0.6436933, y: 0.5693191, z: -0.84098285}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9086936544123485604}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3083210216063876788
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8684952105835262302}
  m_Layer: 9
  m_Name: Bone011
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8684952105835262302
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3083210216063876788}
  serializedVersion: 2
  m_LocalRotation: {x: 0.50885063, y: -0.49336883, z: 0.51201564, w: -0.4852816}
  m_LocalPosition: {x: -0.011095049, y: -0.31482315, z: 0.028914545}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7231935562203167153}
  - {fileID: 8179990922341920946}
  - {fileID: 2903462692686394761}
  m_Father: {fileID: 3131362217246585584}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3219274380976989260
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7234580753912142094}
  - component: {fileID: 6942580202547701836}
  m_Layer: 9
  m_Name: 23_tank_battle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7234580753912142094
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3219274380976989260}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0.00000021137427, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 488320096610899629}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &6942580202547701836
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3219274380976989260}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: cd405a68ac142c84f91d1ac54cf33fba, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -7863909394315025725, guid: d7365b9cd80591f41a90205854c32b00, type: 3}
  m_Bones:
  - {fileID: 5557796227850479151}
  - {fileID: 2137327177826013742}
  - {fileID: 8828924339966949412}
  - {fileID: 8684952105835262302}
  - {fileID: 2903462692686394761}
  - {fileID: 5175751180251130268}
  - {fileID: 7231935562203167153}
  - {fileID: 8179990922341920946}
  - {fileID: 3420938637793754425}
  - {fileID: 5868756291706546860}
  - {fileID: 5995681858760206994}
  - {fileID: 2817991493752010014}
  - {fileID: 946584914870912032}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 2137327177826013742}
  m_AABB:
    m_Center: {x: -0.94828147, y: -0.012547791, z: 0.3895434}
    m_Extent: {x: 1.4774344, y: 1.1955366, z: 1.5284885}
  m_DirtyAABB: 0
--- !u!1 &3368653515952084626
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9086936544123485604}
  m_Layer: 9
  m_Name: Dummy001
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9086936544123485604
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3368653515952084626}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000006657903, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2137327177826013742}
  - {fileID: 5557796227850479151}
  - {fileID: 8828924339966949412}
  - {fileID: 5995681858760206994}
  - {fileID: 2817991493752010014}
  m_Father: {fileID: 488320096610899629}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3716332662789353925
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5175751180251130268}
  m_Layer: 9
  m_Name: Bone009
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5175751180251130268
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3716332662789353925}
  serializedVersion: 2
  m_LocalRotation: {x: -0.025204657, y: -0.021385374, z: -0.7072451, w: 0.7061953}
  m_LocalPosition: {x: 0.17852087, y: 0.4159563, z: -0.093661115}
  m_LocalScale: {x: 0.9999998, y: 1, z: 1.0000008}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5868756291706546860}
  m_Father: {fileID: 3131362217246585584}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3881490252698564856
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8828924339966949412}
  m_Layer: 9
  m_Name: Bone006(mirrored)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8828924339966949412
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3881490252698564856}
  serializedVersion: 2
  m_LocalRotation: {x: 2.5777066e-14, y: -0.00000010811685, z: 0.00000023841858, w: 1}
  m_LocalPosition: {x: -0.6509375, y: 0.569319, z: 0.89461493}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9086936544123485604}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4369531777630866270
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 946584914870912032}
  m_Layer: 9
  m_Name: Bone007
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &946584914870912032
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4369531777630866270}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -1.2246469e-16, w: 1}
  m_LocalPosition: {x: -0.521439, y: 0, z: 0.000000045585676}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5557796227850479151}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4646545494349525776
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3131362217246585584}
  m_Layer: 9
  m_Name: Dummy002
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3131362217246585584
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4646545494349525776}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.0006899911, y: 0.019043883, z: -0.011166915}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4889907820478419166}
  - {fileID: 9140229367415456083}
  - {fileID: 2218561450341055425}
  - {fileID: 5175751180251130268}
  - {fileID: 8684952105835262302}
  m_Father: {fileID: 3735258799252874561}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5650333692510057772
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2903462692686394761}
  m_Layer: 9
  m_Name: Bone012
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2903462692686394761
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5650333692510057772}
  serializedVersion: 2
  m_LocalRotation: {x: 6.123234e-17, y: -6.123234e-17, z: 6.123234e-17, w: 1}
  m_LocalPosition: {x: -0.64009184, y: 0.00000022888183, z: -4.823778e-10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8684952105835262302}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5815613224700978097
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8179990922341920946}
  m_Layer: 9
  m_Name: Bone005
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8179990922341920946
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5815613224700978097}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0071895174, y: -0.01103433, z: 0.7161534, w: 0.69781864}
  m_LocalPosition: {x: -0.37365395, y: -0.08209921, z: -0.8008449}
  m_LocalScale: {x: 1.0000433, y: 0.99999964, z: 1.0000007}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8684952105835262302}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6351898666333711561
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2218561450341055425}
  m_Layer: 9
  m_Name: slot_fire_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2218561450341055425
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6351898666333711561}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 1.3}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 3131362217246585584}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6723914130878546916
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5557796227850479151}
  m_Layer: 9
  m_Name: Bone006
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5557796227850479151
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6723914130878546916}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000023841858, y: 3.162489e-14, z: 1, w: -0.00000013315805}
  m_LocalPosition: {x: 0.6436935, y: 0.5693193, z: 0.89461476}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 946584914870912032}
  m_Father: {fileID: 9086936544123485604}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6919222786818084739
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2817991493752010014}
  m_Layer: 9
  m_Name: Bone008(mirrored)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2817991493752010014
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6919222786818084739}
  serializedVersion: 2
  m_LocalRotation: {x: 2.5777066e-14, y: -0.00000010811685, z: 0.00000023841858, w: 1}
  m_LocalPosition: {x: -0.6509375, y: 0.5693188, z: -0.84098274}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9086936544123485604}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7729599849316939275
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2137327177826013742}
  m_Layer: 9
  m_Name: Bone001
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2137327177826013742
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7729599849316939275}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000026263128, y: 0.0000026464388, z: -0.70980066, w: 0.7044026}
  m_LocalPosition: {x: -0.0036074233, y: 0.5423453, z: -0.3820118}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3420938637793754425}
  - {fileID: 3735258799252874561}
  m_Father: {fileID: 9086936544123485604}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7897729958714894562
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4889907820478419166}
  m_Layer: 9
  m_Name: slot_fire
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4889907820478419166
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7897729958714894562}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.8, y: 0, z: 1.4}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 3131362217246585584}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8739737753019934666
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7231935562203167153}
  m_Layer: 9
  m_Name: Bone003
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7231935562203167153
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8739737753019934666}
  serializedVersion: 2
  m_LocalRotation: {x: -0.007189422, y: -0.0110344235, z: 0.7161534, w: 0.69781864}
  m_LocalPosition: {x: -0.4135146, y: -0.091046475, z: 0.74996084}
  m_LocalScale: {x: 0.9999995, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8684952105835262302}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8841395322783798134
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5868756291706546860}
  m_Layer: 9
  m_Name: Bone010
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5868756291706546860
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8841395322783798134}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.43827987, y: 0.000000057220458, z: -0.000000019960861}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5175751180251130268}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8932406766270621357
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9140229367415456083}
  m_Layer: 9
  m_Name: slot_fire_1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9140229367415456083
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8932406766270621357}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.75, y: 0, z: 1.4}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 3131362217246585584}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9213249262883176453
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2683634742000552013}
  m_Layer: 9
  m_Name: slot_hurt
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2683634742000552013
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9213249262883176453}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.8, z: 0.5}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 488320096610899629}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
