using System;
using System.Collections;
using UnityEngine;

namespace Game.Hotfix
{
    public class UIBattle5V5FightHeroBlood : MonoBehaviour
    {
        private UIImage m_ImgBloodBar;
        private RectTransform m_SelfRT;
        private RectTransform m_ParentRT;
        private GameObject m_GoSkill;
        private UIImage m_ImgSkillIcon;
        private Camera m_Camera;
        
        private EnumBattlePos m_Pos;
        private BattleFiled m_BattleFiled;
        private BattleHero m_BattleHero;
        private bool m_IsWorking;

        private long m_CurHp;
        private long m_MaxHp;

        private Vector2 m_TempV2;
        
        public void Init(EnumBattlePos pos)
        {
            m_Pos = pos;
            
            m_ImgBloodBar = transform.Find("offset/progress")?.GetComponent<UIImage>();
            m_SelfRT = transform.GetComponent<RectTransform>();
            m_ParentRT = transform.parent.GetComponent<RectTransform>();
            m_GoSkill = transform.Find("offset/goSkill")?.gameObject;
            m_ImgSkillIcon = transform.Find("offset/goSkill/imgSkillIcon")?.GetComponent<UIImage>();
        }

        public void Reset(BattleFiled battleFiled)
        {
            m_Camera = GameEntry.Camera.BattleCamera;
            m_BattleFiled = battleFiled;
            m_BattleHero = m_BattleFiled.TeamCtrl.GetBattleHero(m_Pos);
            
            m_IsWorking = m_BattleHero != null;
            gameObject.SetActive(m_IsWorking);

            m_GoSkill.SetActive(false);
        }

        public void PlaySkillCastEffect(HeroBattleSKill skill)
        {
            if (skill != null && !string.IsNullOrEmpty(skill.SkillConfig.icon))
            {
                m_ImgSkillIcon.SetImage(skill.SkillConfig.icon,false, () =>
                {
                    m_GoSkill.SetActive(true);
                    StartCoroutine(PlaySkillCastEffectC());
                });
            }
        }

        IEnumerator PlaySkillCastEffectC()
        {
            yield return new WaitForSeconds(1f);
            m_GoSkill.SetActive(false);
        }
        
        public void SetIsDie(bool isDie)
        {
            if (m_IsWorking)
            {
                gameObject.SetActive(false);
            }
        }
        
        private void Update()
        {
            if (!m_IsWorking)
                return;
            
            //设置位置
            if (m_BattleHero != null)
            {
                m_SelfRT.anchoredPosition = WorldPosToScreenPos(m_BattleHero.GetPosition());    
            }
            
            m_BattleHero?.GetHPState(out m_CurHp, out m_MaxHp);
            if (m_MaxHp > 0)
                m_ImgBloodBar.fillAmount = (float)m_CurHp / m_MaxHp;
            else
                m_ImgBloodBar.fillAmount = 0;
        }
        
        private Vector2 WorldPosToScreenPos(Vector3 worldPos)
        {
            var screenPoint = RectTransformUtility.WorldToScreenPoint(m_Camera, worldPos);
            RectTransformUtility.ScreenPointToLocalPointInRectangle(m_ParentRT, screenPoint,
                GameEntry.Camera.UICamera, out m_TempV2);
            return m_TempV2;
        }
    }
}