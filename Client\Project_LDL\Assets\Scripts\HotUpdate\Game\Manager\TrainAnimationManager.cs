using UnityEngine;
using System;

namespace Game.Hotfix
{
    /// <summary>
    /// 火车动画管理器
    /// 提供简单的事件驱动接口来控制火车动画
    /// </summary>
    public class TrainAnimationManager : MonoBehaviour
    {
        private static TrainAnimationManager s_Instance;
        
        /// <summary>
        /// 单例实例
        /// </summary>
        public static TrainAnimationManager Instance
        {
            get
            {
                if (s_Instance == null)
                {
                    GameObject go = new GameObject("TrainAnimationManager");
                    s_Instance = go.AddComponent<TrainAnimationManager>();
                    DontDestroyOnLoad(go);
                }
                return s_Instance;
            }
        }

        /// <summary>
        /// 火车动画开始事件
        /// </summary>
        public static event Action<int, TrainAnimationType> OnTrainAnimationStarted;
        
        /// <summary>
        /// 火车动画完成事件
        /// </summary>
        public static event Action<int, TrainAnimationType> OnTrainAnimationCompleted;
        
        /// <summary>
        /// 火车动画停止事件
        /// </summary>
        public static event Action<int, TrainAnimationType> OnTrainAnimationStopped;
        
        /// <summary>
        /// 火车动画失败事件
        /// </summary>
        public static event Action<int, TrainAnimationType> OnTrainAnimationFailed;

        private void Awake()
        {
            if (s_Instance == null)
            {
                s_Instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else if (s_Instance != this)
            {
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// 请求播放火车进站动画
        /// </summary>
        /// <param name="trainStationId">火车站ID，0表示所有火车站</param>
        /// <param name="duration">动画时长</param>
        public static void RequestEnterAnimation(int trainStationId = 0, float duration = 2f)
        {
            RequestAnimation(trainStationId, TrainAnimationType.Enter, duration);
        }

        /// <summary>
        /// 请求播放火车出站动画
        /// </summary>
        /// <param name="trainStationId">火车站ID，0表示所有火车站</param>
        /// <param name="duration">动画时长</param>
        public static void RequestExitAnimation(int trainStationId = 0, float duration = 2f)
        {
            RequestAnimation(trainStationId, TrainAnimationType.Exit, duration);
        }

        /// <summary>
        /// 请求停止火车动画
        /// </summary>
        /// <param name="trainStationId">火车站ID，0表示所有火车站</param>
        public static void RequestStopAnimation(int trainStationId = 0)
        {
            RequestAnimation(trainStationId, TrainAnimationType.Stop, 0f);
        }

        /// <summary>
        /// 请求火车瞬移到起始点
        /// </summary>
        /// <param name="trainStationId">火车站ID，0表示所有火车站</param>
        public static void RequestTeleportToStart(int trainStationId = 0)
        {
            RequestAnimation(trainStationId, TrainAnimationType.TeleportStart, 0f);
        }

        /// <summary>
        /// 请求火车瞬移到停靠点
        /// </summary>
        /// <param name="trainStationId">火车站ID，0表示所有火车站</param>
        public static void RequestTeleportToStay(int trainStationId = 0)
        {
            RequestAnimation(trainStationId, TrainAnimationType.TeleportStay, 0f);
        }

        /// <summary>
        /// 请求火车瞬移到终点
        /// </summary>
        /// <param name="trainStationId">火车站ID，0表示所有火车站</param>
        public static void RequestTeleportToEnd(int trainStationId = 0)
        {
            RequestAnimation(trainStationId, TrainAnimationType.TeleportEnd, 0f);
        }

        /// <summary>
        /// 请求火车动画
        /// </summary>
        /// <param name="trainStationId">火车站ID</param>
        /// <param name="animationType">动画类型</param>
        /// <param name="duration">动画时长</param>
        private static void RequestAnimation(int trainStationId, TrainAnimationType animationType, float duration)
        {
            try
            {
                // 通过事件系统发送请求
                GameEntry.Event.Fire(Instance, TrainAnimationRequestEventArgs.Create(trainStationId, animationType, duration));
                ColorLog.Green($"发送火车动画请求: 站点ID={trainStationId}, 类型={animationType}, 时长={duration}");
            }
            catch (Exception ex)
            {
                ColorLog.Red($"发送火车动画请求失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理火车动画状态事件
        /// </summary>
        /// <param name="trainStationId">火车站ID</param>
        /// <param name="animationType">动画类型</param>
        /// <param name="status">动画状态</param>
        public static void HandleAnimationStatus(int trainStationId, TrainAnimationType animationType, TrainAnimationStatus status)
        {
            switch (status)
            {
                case TrainAnimationStatus.Started:
                    OnTrainAnimationStarted?.Invoke(trainStationId, animationType);
                    ColorLog.Green($"火车动画开始: 站点ID={trainStationId}, 类型={animationType}");
                    break;

                case TrainAnimationStatus.Completed:
                    OnTrainAnimationCompleted?.Invoke(trainStationId, animationType);
                    ColorLog.Green($"火车动画完成: 站点ID={trainStationId}, 类型={animationType}");
                    break;

                case TrainAnimationStatus.Stopped:
                    OnTrainAnimationStopped?.Invoke(trainStationId, animationType);
                    ColorLog.Yellow($"火车动画停止: 站点ID={trainStationId}, 类型={animationType}");
                    break;

                case TrainAnimationStatus.Failed:
                    OnTrainAnimationFailed?.Invoke(trainStationId, animationType);
                    ColorLog.Red($"火车动画失败: 站点ID={trainStationId}, 类型={animationType}");
                    break;
            }
        }

        private void OnDestroy()
        {
            if (s_Instance == this)
            {
                s_Instance = null;
            }
        }
    }

    /// <summary>
    /// 火车动画类型
    /// </summary>
    public enum TrainAnimationType
    {
        None,           // 无
        Enter,          // 进站
        Exit,           // 出站
        Stop,           // 停止动画
        TeleportStart,  // 瞬移到起始点
        TeleportStay,   // 瞬移到停靠点
        TeleportEnd     // 瞬移到终点
    }

    /// <summary>
    /// 火车动画状态
    /// </summary>
    public enum TrainAnimationStatus
    {
        None,       // 无
        Started,    // 开始
        Completed,  // 完成
        Stopped,    // 停止
        Failed      // 失败
    }

    /// <summary>
    /// 火车动画请求事件参数
    /// </summary>
    public class TrainAnimationRequestEventArgs
    {
        public int TrainStationId { get; set; }
        public TrainAnimationType AnimationType { get; set; }
        public float Duration { get; set; }
        public object UserData { get; set; }

        public static TrainAnimationRequestEventArgs Create(int trainStationId, TrainAnimationType animationType, float duration = 2f, object userData = null)
        {
            return new TrainAnimationRequestEventArgs
            {
                TrainStationId = trainStationId,
                AnimationType = animationType,
                Duration = duration,
                UserData = userData
            };
        }
    }

    /// <summary>
    /// 火车动画状态事件参数
    /// </summary>
    public class TrainAnimationStatusEventArgs
    {
        public int TrainStationId { get; set; }
        public TrainAnimationType AnimationType { get; set; }
        public TrainAnimationStatus Status { get; set; }
        public object UserData { get; set; }

        public static TrainAnimationStatusEventArgs Create(int trainStationId, TrainAnimationType animationType, TrainAnimationStatus status, object userData = null)
        {
            return new TrainAnimationStatusEventArgs
            {
                TrainStationId = trainStationId,
                AnimationType = animationType,
                Status = status,
                UserData = userData
            };
        }
    }
}
