using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UITradeTrainBattleResultForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnBack;
        [SerializeField] private UIButton m_btnShare;
        [SerializeField] private UIButton m_btnMail;

        [SerializeField] private Transform m_transHeroItem;
        [SerializeField] private Transform m_transPlayerLeft;
        [SerializeField] private Transform m_transPlayerRight;
        [SerializeField] private Transform m_transTeamLeft;
        [SerializeField] private Transform m_transTeamRight;

        void InitBind()
        {
            m_btnBack.onClick.AddListener(OnBtnBackClick);
            m_btnShare.onClick.AddListener(OnBtnShareClick);
            m_btnMail.onClick.AddListener(OnBtnMailClick);
        }
    }
}
