using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;
using Game.Hotfix.Config;

namespace Game.Hotfix
{
    public partial class UITradeTrainBattleRecordForm : UGuiFormEx
    {
        Trade.TradeTrainFightRecordListResp record;
        List<Trade.TradeTrainFightRecord> recordList = new();

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            InitView();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            m_goRecordItem.SetActive(false);

            if (userData is Trade.TradeTrainFightRecordListResp data)
            {
                record = data;

                if (record != null)
                {
                    recordList = new(record.FightRecords);
                }
            }

            RefreshPanel();
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        void InitView()
        {
            m_TableViewV.GetItemCount = () =>
            {
                return recordList.Count;
            };
            m_TableViewV.GetItemGo = () => m_goRecordItem;
            m_TableViewV.UpdateItemCell = UpdateView;
            m_TableViewV.InitTableViewByIndex(0, 12);
        }

        void UpdateView(int index, GameObject obj)
        {
            UIText txtTime = obj.transform.Find("bg/top/txtTime").GetComponent<UIText>();
            UIText txtName = obj.transform.Find("bg/txtName").GetComponent<UIText>();
            UIText txtArea = obj.transform.Find("bg/txtArea").GetComponent<UIText>();
            UIText txtPower = obj.transform.Find("bg/txtPower").GetComponent<UIText>();
            Transform rewardParent = obj.transform.Find("bg/reward/Scroll View/Viewport/Content");

            Trade.TradeTrainFightRecord fightRecord = recordList[index];

            List<Article.Article> articles = new(fightRecord.Articles);
            RefreshReward(articles, rewardParent, m_transReward);

            GameEntry.RoleData.RequestRoleQueryLocalSingle(fightRecord.PredatorId, (roleBrief) =>
            {
                ColorLog.Pink("查询掠夺者信息", roleBrief);
                if (roleBrief != null)
                {
                    txtName.text = roleBrief.Name;
                    txtPower.text = ToolScriptExtend.FormatNumberWithSeparator(roleBrief.Power);
                }
            });
        }

        void RefreshPanel()
        {
            if (m_TableViewV.itemPrototype == null)
            {
                m_TableViewV.InitTableViewByIndex(0, 12);
            }
            else
            {
                m_TableViewV.ReloadData();
            }

            m_goNoData.SetActive(recordList.Count == 0);
        }
        
        void RefreshReward(List<Article.Article> rewards, Transform transContentReward, Transform transReward)
        {
            foreach (Transform item in transContentReward)
            {
                item.gameObject.SetActive(false);
            }

            for (int i = 0; i < rewards.Count; i++)
            {
                if (i < transContentReward.childCount)
                {
                    transContentReward.GetChild(i).gameObject.SetActive(true);
                    if (transContentReward.GetChild(i).childCount > 0)
                    {
                        UIItemModule uiItemModule = transContentReward.GetChild(i).GetChild(0).GetComponent<UIItemModule>();
                        if (uiItemModule != null)
                        {
                            uiItemModule.SetData((itemid)rewards[i].Code, rewards[i].Amount);
                            uiItemModule.InitConfigData();
                            uiItemModule.DisplayInfo();
                        }
                    }
                }
                else
                {
                    int j = i;
                    Transform transRewardItem = Instantiate(transReward, transContentReward);
                    BagManager.CreatItem(transRewardItem, (itemid)rewards[i].Code, rewards[i].Amount, (item) =>
                    {
                        item.GetComponent<UIButton>().useTween = false;
                        item.SetClick(item.OpenTips);
                    });
                }
            }
        }
    }
}
