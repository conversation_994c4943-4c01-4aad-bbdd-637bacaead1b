{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 10784, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 10784, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 10784, "tid": 9659, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 10784, "tid": 9659, "ts": 1750748283583300, "dur": 526, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 10784, "tid": 9659, "ts": 1750748283585464, "dur": 529, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 10784, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 10784, "tid": 1, "ts": **********650298, "dur": 3608, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 10784, "tid": 1, "ts": **********653911, "dur": 70431, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 10784, "tid": 1, "ts": **********724362, "dur": 407978, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 10784, "tid": 9659, "ts": 1750748283585997, "dur": 20, "ph": "X", "name": "", "args": {}}, {"pid": 10784, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 10784, "tid": 12884901888, "ts": **********648825, "dur": 29337, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********678166, "dur": 2896606, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********678776, "dur": 1267, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********680053, "dur": 1022, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********681082, "dur": 493, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********681583, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********681589, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********681654, "dur": 434, "ph": "X", "name": "ProcessMessages 1668", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682094, "dur": 129, "ph": "X", "name": "ReadAsync 1668", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682231, "dur": 13, "ph": "X", "name": "ProcessMessages 14290", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682247, "dur": 39, "ph": "X", "name": "ReadAsync 14290", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682287, "dur": 1, "ph": "X", "name": "ProcessMessages 1754", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682290, "dur": 17, "ph": "X", "name": "ReadAsync 1754", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682310, "dur": 13, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682326, "dur": 29, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682364, "dur": 3, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682370, "dur": 41, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682413, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682416, "dur": 27, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682445, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682447, "dur": 138, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682589, "dur": 2, "ph": "X", "name": "ProcessMessages 856", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682592, "dur": 68, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682667, "dur": 6, "ph": "X", "name": "ProcessMessages 5505", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682676, "dur": 57, "ph": "X", "name": "ReadAsync 5505", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682740, "dur": 3, "ph": "X", "name": "ProcessMessages 959", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682745, "dur": 48, "ph": "X", "name": "ReadAsync 959", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682796, "dur": 2, "ph": "X", "name": "ProcessMessages 2028", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682799, "dur": 26, "ph": "X", "name": "ReadAsync 2028", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682827, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682829, "dur": 28, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682859, "dur": 1, "ph": "X", "name": "ProcessMessages 1099", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682861, "dur": 21, "ph": "X", "name": "ReadAsync 1099", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682886, "dur": 23, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682910, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682912, "dur": 20, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682933, "dur": 1, "ph": "X", "name": "ProcessMessages 806", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682935, "dur": 20, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682957, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682959, "dur": 20, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********682982, "dur": 23, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683007, "dur": 1, "ph": "X", "name": "ProcessMessages 884", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683009, "dur": 20, "ph": "X", "name": "ReadAsync 884", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683031, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683033, "dur": 18, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683054, "dur": 19, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683076, "dur": 19, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683098, "dur": 15, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683117, "dur": 83, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683206, "dur": 2, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683210, "dur": 32, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683246, "dur": 2, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683250, "dur": 156, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683413, "dur": 3, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683418, "dur": 50, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683470, "dur": 3, "ph": "X", "name": "ProcessMessages 3216", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683474, "dur": 26, "ph": "X", "name": "ReadAsync 3216", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683507, "dur": 3, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683513, "dur": 46, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683564, "dur": 3, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683569, "dur": 35, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683606, "dur": 1, "ph": "X", "name": "ProcessMessages 1196", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683609, "dur": 26, "ph": "X", "name": "ReadAsync 1196", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683641, "dur": 3, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683647, "dur": 31, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683684, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683720, "dur": 1, "ph": "X", "name": "ProcessMessages 1107", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683723, "dur": 23, "ph": "X", "name": "ReadAsync 1107", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683750, "dur": 2, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683753, "dur": 19, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683776, "dur": 15, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683794, "dur": 12, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683810, "dur": 146, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683959, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********683961, "dur": 44, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684007, "dur": 3, "ph": "X", "name": "ProcessMessages 3997", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684011, "dur": 27, "ph": "X", "name": "ReadAsync 3997", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684044, "dur": 2, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684048, "dur": 29, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684081, "dur": 2, "ph": "X", "name": "ProcessMessages 1061", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684085, "dur": 17, "ph": "X", "name": "ReadAsync 1061", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684105, "dur": 1, "ph": "X", "name": "ProcessMessages 77", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684109, "dur": 18, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684130, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684133, "dur": 27, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684162, "dur": 2, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684166, "dur": 14, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684183, "dur": 15, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684205, "dur": 3, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684210, "dur": 24, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684237, "dur": 68, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684309, "dur": 3, "ph": "X", "name": "ProcessMessages 1459", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684313, "dur": 29, "ph": "X", "name": "ReadAsync 1459", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684344, "dur": 1, "ph": "X", "name": "ProcessMessages 1126", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684346, "dur": 23, "ph": "X", "name": "ReadAsync 1126", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684375, "dur": 2, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684380, "dur": 25, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684408, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684412, "dur": 34, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684450, "dur": 2, "ph": "X", "name": "ProcessMessages 1034", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684454, "dur": 32, "ph": "X", "name": "ReadAsync 1034", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684491, "dur": 3, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684496, "dur": 27, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684526, "dur": 2, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684529, "dur": 18, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684551, "dur": 26, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684580, "dur": 18, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684600, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684602, "dur": 15, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684620, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684622, "dur": 15, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684640, "dur": 19, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684662, "dur": 15, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684680, "dur": 15, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684698, "dur": 14, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684716, "dur": 10, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684728, "dur": 15, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684746, "dur": 14, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684761, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684763, "dur": 14, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684781, "dur": 14, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684799, "dur": 16, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684818, "dur": 16, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684836, "dur": 1, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684838, "dur": 19, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684859, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684860, "dur": 15, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684880, "dur": 12, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684895, "dur": 19, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684918, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684921, "dur": 25, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684949, "dur": 1, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684953, "dur": 29, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684985, "dur": 2, "ph": "X", "name": "ProcessMessages 761", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********684989, "dur": 18, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685010, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685014, "dur": 45, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685064, "dur": 3, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685069, "dur": 25, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685095, "dur": 1, "ph": "X", "name": "ProcessMessages 1084", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685098, "dur": 13, "ph": "X", "name": "ReadAsync 1084", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685114, "dur": 9, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685127, "dur": 12, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685143, "dur": 14, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685159, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685161, "dur": 11, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685175, "dur": 13, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685190, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685192, "dur": 16, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685210, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685211, "dur": 11, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685226, "dur": 18, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685246, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685248, "dur": 12, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685263, "dur": 12, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685279, "dur": 27, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685312, "dur": 3, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685318, "dur": 29, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685349, "dur": 1, "ph": "X", "name": "ProcessMessages 1155", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685351, "dur": 15, "ph": "X", "name": "ReadAsync 1155", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685370, "dur": 14, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685388, "dur": 12, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685403, "dur": 16, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685420, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685423, "dur": 12, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685438, "dur": 17, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685458, "dur": 59, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685519, "dur": 1, "ph": "X", "name": "ProcessMessages 1887", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685521, "dur": 134, "ph": "X", "name": "ReadAsync 1887", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685658, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685677, "dur": 12, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685693, "dur": 11, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685707, "dur": 14, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685724, "dur": 12, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685738, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685740, "dur": 12, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685755, "dur": 11, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685769, "dur": 13, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685785, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685805, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685806, "dur": 14, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685824, "dur": 10, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685837, "dur": 13, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685853, "dur": 14, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685869, "dur": 1, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685871, "dur": 18, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685892, "dur": 16, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685911, "dur": 9, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685922, "dur": 1, "ph": "X", "name": "ProcessMessages 202", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685924, "dur": 11, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685938, "dur": 14, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685953, "dur": 1, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685955, "dur": 15, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685974, "dur": 14, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********685991, "dur": 12, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686007, "dur": 17, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686027, "dur": 16, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686047, "dur": 15, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686065, "dur": 13, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686080, "dur": 11, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686095, "dur": 11, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686108, "dur": 15, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686127, "dur": 46, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686175, "dur": 1, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686177, "dur": 17, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686198, "dur": 12, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686213, "dur": 12, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686227, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686241, "dur": 20, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686264, "dur": 18, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686285, "dur": 16, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686305, "dur": 12, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686319, "dur": 12, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686334, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686351, "dur": 12, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686366, "dur": 15, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686384, "dur": 30, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686418, "dur": 13, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686434, "dur": 12, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686449, "dur": 12, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686464, "dur": 13, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686478, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686481, "dur": 12, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686497, "dur": 14, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686514, "dur": 11, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686528, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686553, "dur": 3, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686559, "dur": 29, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686590, "dur": 1, "ph": "X", "name": "ProcessMessages 1554", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686593, "dur": 17, "ph": "X", "name": "ReadAsync 1554", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686612, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686614, "dur": 33, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686650, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686684, "dur": 16, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686702, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686703, "dur": 22, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686728, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686730, "dur": 14, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686746, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686747, "dur": 14, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686764, "dur": 14, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686781, "dur": 19, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686804, "dur": 14, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686821, "dur": 12, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686836, "dur": 12, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686852, "dur": 11, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686865, "dur": 13, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686881, "dur": 14, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686899, "dur": 13, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686915, "dur": 17, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686934, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686936, "dur": 12, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686951, "dur": 14, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686968, "dur": 12, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686982, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********686984, "dur": 17, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687004, "dur": 13, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687021, "dur": 11, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687035, "dur": 12, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687050, "dur": 14, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687067, "dur": 15, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687085, "dur": 15, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687104, "dur": 12, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687119, "dur": 13, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687135, "dur": 12, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687149, "dur": 11, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687163, "dur": 13, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687180, "dur": 14, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687196, "dur": 11, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687210, "dur": 12, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687226, "dur": 40, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687268, "dur": 1, "ph": "X", "name": "ProcessMessages 1398", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687270, "dur": 13, "ph": "X", "name": "ReadAsync 1398", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687286, "dur": 12, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687301, "dur": 20, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687323, "dur": 12, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687339, "dur": 13, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687355, "dur": 10, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687368, "dur": 13, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687384, "dur": 13, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687400, "dur": 11, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687414, "dur": 14, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687431, "dur": 14, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687448, "dur": 15, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687465, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687467, "dur": 13, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687484, "dur": 12, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687499, "dur": 12, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687514, "dur": 19, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687536, "dur": 12, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687550, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687553, "dur": 13, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687569, "dur": 10, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687582, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687607, "dur": 14, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687625, "dur": 14, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687643, "dur": 13, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687687, "dur": 20, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687709, "dur": 1, "ph": "X", "name": "ProcessMessages 1177", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687712, "dur": 13, "ph": "X", "name": "ReadAsync 1177", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687728, "dur": 14, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687744, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687745, "dur": 27, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687776, "dur": 22, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687801, "dur": 19, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687822, "dur": 1, "ph": "X", "name": "ProcessMessages 1029", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687824, "dur": 25, "ph": "X", "name": "ReadAsync 1029", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687853, "dur": 2, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687857, "dur": 26, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687886, "dur": 15, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687903, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687905, "dur": 16, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687924, "dur": 19, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687945, "dur": 11, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687960, "dur": 16, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********687980, "dur": 21, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688006, "dur": 2, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688011, "dur": 31, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688045, "dur": 2, "ph": "X", "name": "ProcessMessages 923", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688049, "dur": 21, "ph": "X", "name": "ReadAsync 923", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688073, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688089, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688091, "dur": 19, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688113, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688115, "dur": 15, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688133, "dur": 13, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688148, "dur": 15, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688167, "dur": 11, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688181, "dur": 14, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688199, "dur": 29, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688230, "dur": 1, "ph": "X", "name": "ProcessMessages 761", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688231, "dur": 11, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688246, "dur": 13, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688261, "dur": 13, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688277, "dur": 18, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688299, "dur": 22, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688323, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688325, "dur": 20, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688350, "dur": 2, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688355, "dur": 22, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688379, "dur": 1, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688381, "dur": 20, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688408, "dur": 3, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688414, "dur": 34, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688453, "dur": 2, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688458, "dur": 34, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688495, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688497, "dur": 22, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688522, "dur": 20, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688546, "dur": 15, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688562, "dur": 1, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688564, "dur": 17, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688585, "dur": 2, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688590, "dur": 21, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688612, "dur": 1, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688614, "dur": 31, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688649, "dur": 2, "ph": "X", "name": "ProcessMessages 1026", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688653, "dur": 15, "ph": "X", "name": "ReadAsync 1026", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688670, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688673, "dur": 20, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688694, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688696, "dur": 15, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688714, "dur": 15, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688731, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688733, "dur": 15, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688751, "dur": 15, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688770, "dur": 13, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688786, "dur": 13, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688802, "dur": 13, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688819, "dur": 16, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688837, "dur": 15, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688856, "dur": 14, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688873, "dur": 13, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688889, "dur": 15, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688908, "dur": 43, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688953, "dur": 1, "ph": "X", "name": "ProcessMessages 1109", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688957, "dur": 12, "ph": "X", "name": "ReadAsync 1109", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688971, "dur": 22, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********688997, "dur": 2, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689001, "dur": 23, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689025, "dur": 1, "ph": "X", "name": "ProcessMessages 1379", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689028, "dur": 18, "ph": "X", "name": "ReadAsync 1379", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689049, "dur": 13, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689065, "dur": 18, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689087, "dur": 2, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689091, "dur": 43, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689135, "dur": 1, "ph": "X", "name": "ProcessMessages 1373", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689138, "dur": 15, "ph": "X", "name": "ReadAsync 1373", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689156, "dur": 11, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689170, "dur": 19, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689191, "dur": 1, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689193, "dur": 16, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689211, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689213, "dur": 16, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689233, "dur": 13, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689251, "dur": 15, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689269, "dur": 39, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689312, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689315, "dur": 24, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689341, "dur": 1, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689343, "dur": 21, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689368, "dur": 15, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689385, "dur": 1, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689387, "dur": 17, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689406, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689408, "dur": 22, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689434, "dur": 25, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689462, "dur": 32, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689498, "dur": 2, "ph": "X", "name": "ProcessMessages 974", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689502, "dur": 37, "ph": "X", "name": "ReadAsync 974", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689542, "dur": 1, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689545, "dur": 28, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689575, "dur": 1, "ph": "X", "name": "ProcessMessages 1213", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689577, "dur": 19, "ph": "X", "name": "ReadAsync 1213", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689599, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689602, "dur": 23, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689626, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689628, "dur": 25, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689658, "dur": 2, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689663, "dur": 180, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689847, "dur": 2, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689851, "dur": 67, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689921, "dur": 5, "ph": "X", "name": "ProcessMessages 5945", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689927, "dur": 15, "ph": "X", "name": "ReadAsync 5945", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689945, "dur": 29, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689977, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********689980, "dur": 18, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690001, "dur": 21, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690028, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690032, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690061, "dur": 2, "ph": "X", "name": "ProcessMessages 991", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690065, "dur": 44, "ph": "X", "name": "ReadAsync 991", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690114, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690118, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690141, "dur": 1, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690144, "dur": 23, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690170, "dur": 24, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690196, "dur": 1, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690198, "dur": 18, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690219, "dur": 11, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690232, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690234, "dur": 11, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690248, "dur": 20, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690271, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690286, "dur": 47, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690335, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690354, "dur": 13, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690370, "dur": 19, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690393, "dur": 20, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690415, "dur": 19, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690437, "dur": 11, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690452, "dur": 33, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690487, "dur": 10, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690501, "dur": 35, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690540, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690542, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690563, "dur": 1, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690566, "dur": 24, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690594, "dur": 23, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690619, "dur": 1, "ph": "X", "name": "ProcessMessages 900", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690622, "dur": 18, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690643, "dur": 12, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690658, "dur": 13, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690674, "dur": 1, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690676, "dur": 45, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690724, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690742, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690744, "dur": 16, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690763, "dur": 13, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690780, "dur": 34, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690818, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690844, "dur": 17, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690862, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690864, "dur": 23, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690890, "dur": 14, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690908, "dur": 14, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690925, "dur": 12, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690940, "dur": 22, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********690965, "dur": 51, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691020, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691024, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691047, "dur": 2, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691050, "dur": 19, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691072, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691075, "dur": 22, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691099, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691101, "dur": 19, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691123, "dur": 2, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691127, "dur": 16, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691146, "dur": 33, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691181, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691199, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691200, "dur": 17, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691220, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691222, "dur": 12, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691237, "dur": 12, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691252, "dur": 17, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691272, "dur": 13, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691288, "dur": 11, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691303, "dur": 30, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691336, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691358, "dur": 23, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691385, "dur": 2, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691388, "dur": 36, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691427, "dur": 14, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691445, "dur": 14, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691464, "dur": 18, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691488, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691493, "dur": 27, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691524, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691541, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691543, "dur": 22, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691569, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691592, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691595, "dur": 17, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691615, "dur": 15, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691633, "dur": 12, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691648, "dur": 10, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691661, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691700, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691724, "dur": 14, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691741, "dur": 12, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691756, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691786, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691819, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691822, "dur": 20, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691845, "dur": 12, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691860, "dur": 26, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691891, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691909, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691911, "dur": 12, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691927, "dur": 36, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691967, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691984, "dur": 13, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********691999, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692001, "dur": 11, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692015, "dur": 27, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692045, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692069, "dur": 15, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692088, "dur": 10, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692101, "dur": 25, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692129, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692145, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692147, "dur": 14, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692164, "dur": 11, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692178, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692210, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692226, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692228, "dur": 14, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692244, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692245, "dur": 10, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692258, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692285, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692309, "dur": 16, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692329, "dur": 10, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692342, "dur": 23, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692367, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692383, "dur": 12, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692399, "dur": 12, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692414, "dur": 28, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692445, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692463, "dur": 15, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692479, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692481, "dur": 11, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692496, "dur": 28, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692527, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692556, "dur": 12, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692571, "dur": 16, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692591, "dur": 11, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692605, "dur": 26, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692634, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692652, "dur": 15, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692670, "dur": 10, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692683, "dur": 28, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692715, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692732, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692734, "dur": 13, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692750, "dur": 35, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692789, "dur": 149, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692940, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692943, "dur": 37, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692982, "dur": 3, "ph": "X", "name": "ProcessMessages 4228", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********692987, "dur": 14, "ph": "X", "name": "ReadAsync 4228", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693003, "dur": 15, "ph": "X", "name": "ReadAsync 122", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693021, "dur": 15, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693041, "dur": 12, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693057, "dur": 29, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693089, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693104, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693106, "dur": 18, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693126, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693128, "dur": 32, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693164, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693182, "dur": 14, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693199, "dur": 9, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693212, "dur": 26, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693240, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693257, "dur": 13, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693274, "dur": 12, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693288, "dur": 39, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693334, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693338, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693383, "dur": 2, "ph": "X", "name": "ProcessMessages 1044", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693387, "dur": 27, "ph": "X", "name": "ReadAsync 1044", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693418, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693450, "dur": 1, "ph": "X", "name": "ProcessMessages 858", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693452, "dur": 14, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693469, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693471, "dur": 55, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693531, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693534, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693557, "dur": 2, "ph": "X", "name": "ProcessMessages 862", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693560, "dur": 12, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693576, "dur": 36, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693615, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693634, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693636, "dur": 13, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693652, "dur": 15, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693669, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693671, "dur": 16, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693690, "dur": 17, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693710, "dur": 13, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693725, "dur": 1, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693727, "dur": 12, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693743, "dur": 40, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693786, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693805, "dur": 15, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693823, "dur": 23, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693850, "dur": 15, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693867, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693869, "dur": 13, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693884, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693886, "dur": 11, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693900, "dur": 11, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693914, "dur": 33, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693949, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693969, "dur": 14, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********693986, "dur": 13, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694002, "dur": 16, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694021, "dur": 15, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694039, "dur": 13, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694054, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694056, "dur": 14, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694074, "dur": 32, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694109, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694126, "dur": 14, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694143, "dur": 16, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694160, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694162, "dur": 12, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694178, "dur": 30, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694211, "dur": 17, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694231, "dur": 37, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694271, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694290, "dur": 13, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694306, "dur": 14, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694323, "dur": 14, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694340, "dur": 43, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694385, "dur": 2, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694388, "dur": 13, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694404, "dur": 35, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694442, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694457, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694459, "dur": 13, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694475, "dur": 13, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694492, "dur": 13, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694507, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694509, "dur": 18, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694530, "dur": 11, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694544, "dur": 11, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694558, "dur": 31, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694593, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694613, "dur": 17, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694635, "dur": 2, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694639, "dur": 20, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694661, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694663, "dur": 15, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694679, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694681, "dur": 13, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694698, "dur": 13, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694714, "dur": 33, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694751, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694769, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694771, "dur": 25, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694798, "dur": 1, "ph": "X", "name": "ProcessMessages 1259", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694800, "dur": 16, "ph": "X", "name": "ReadAsync 1259", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694820, "dur": 1, "ph": "X", "name": "ProcessMessages 671", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694822, "dur": 18, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694843, "dur": 13, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694859, "dur": 1, "ph": "X", "name": "ProcessMessages 229", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694862, "dur": 13, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694878, "dur": 22, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694903, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694921, "dur": 15, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694938, "dur": 30, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694972, "dur": 15, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694988, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********694990, "dur": 22, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695014, "dur": 1, "ph": "X", "name": "ProcessMessages 761", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695017, "dur": 12, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695032, "dur": 12, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695074, "dur": 14, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695091, "dur": 15, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695108, "dur": 12, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695123, "dur": 12, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695139, "dur": 38, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695181, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695195, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695197, "dur": 13, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695213, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695215, "dur": 19, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695238, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695241, "dur": 15, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695259, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695283, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695302, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695304, "dur": 16, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695324, "dur": 26, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695358, "dur": 1, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695360, "dur": 15, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695379, "dur": 13, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695396, "dur": 39, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695438, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695457, "dur": 16, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695477, "dur": 36, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695515, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695517, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695541, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695542, "dur": 13, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695559, "dur": 27, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695590, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695609, "dur": 52, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695665, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695668, "dur": 25, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695697, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695714, "dur": 14, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695731, "dur": 18, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695751, "dur": 1, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695753, "dur": 17, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695771, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695773, "dur": 14, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695790, "dur": 12, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695805, "dur": 39, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695848, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695870, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695873, "dur": 15, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695892, "dur": 26, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695920, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695937, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695939, "dur": 15, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695956, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********695957, "dur": 60, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696021, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696038, "dur": 16, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696056, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696058, "dur": 12, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696073, "dur": 25, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696101, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696120, "dur": 17, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696142, "dur": 31, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696177, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696194, "dur": 14, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696212, "dur": 14, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696229, "dur": 11, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696243, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696269, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696288, "dur": 17, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696308, "dur": 15, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696327, "dur": 15, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696353, "dur": 26, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696381, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696384, "dur": 14, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696401, "dur": 20, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696424, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696442, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696444, "dur": 14, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696462, "dur": 13, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696479, "dur": 16, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696498, "dur": 17, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696518, "dur": 13, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696535, "dur": 13, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696551, "dur": 41, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696597, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696601, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696639, "dur": 3, "ph": "X", "name": "ProcessMessages 1126", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696643, "dur": 17, "ph": "X", "name": "ReadAsync 1126", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696666, "dur": 18, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696686, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696689, "dur": 34, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696730, "dur": 3, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696736, "dur": 29, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696769, "dur": 2, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696773, "dur": 33, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696809, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696811, "dur": 18, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696833, "dur": 56, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696892, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696907, "dur": 12, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696923, "dur": 14, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696940, "dur": 11, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696955, "dur": 34, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********696992, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697010, "dur": 20, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697034, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697037, "dur": 35, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697074, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697076, "dur": 62, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697142, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697145, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697199, "dur": 2, "ph": "X", "name": "ProcessMessages 1157", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697203, "dur": 70, "ph": "X", "name": "ReadAsync 1157", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697279, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697283, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697314, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697318, "dur": 33, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697356, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697359, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697404, "dur": 3, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697410, "dur": 23, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697435, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697438, "dur": 26, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697468, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697485, "dur": 1, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697487, "dur": 13, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697504, "dur": 44, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697552, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697601, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697604, "dur": 24, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697631, "dur": 15, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697649, "dur": 13, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697664, "dur": 31, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697697, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697699, "dur": 17, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697720, "dur": 13, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697736, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697752, "dur": 12, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697767, "dur": 18, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697789, "dur": 15, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697808, "dur": 13, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697824, "dur": 12, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697837, "dur": 1, "ph": "X", "name": "ProcessMessages 71", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697840, "dur": 17, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697860, "dur": 31, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697896, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697926, "dur": 2, "ph": "X", "name": "ProcessMessages 966", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697930, "dur": 17, "ph": "X", "name": "ReadAsync 966", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697952, "dur": 16, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697972, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697991, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********697993, "dur": 16, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698012, "dur": 10, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698026, "dur": 62, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698092, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698107, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698109, "dur": 17, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698130, "dur": 33, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698165, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698183, "dur": 14, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698200, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698230, "dur": 10, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698243, "dur": 30, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698276, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698293, "dur": 14, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698311, "dur": 10, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698324, "dur": 26, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698353, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698367, "dur": 33, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698404, "dur": 30, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698441, "dur": 3, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698447, "dur": 34, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698483, "dur": 1, "ph": "X", "name": "ProcessMessages 1219", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698485, "dur": 29, "ph": "X", "name": "ReadAsync 1219", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698519, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698547, "dur": 3, "ph": "X", "name": "ProcessMessages 823", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698553, "dur": 22, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698577, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698579, "dur": 33, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698618, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698622, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698650, "dur": 2, "ph": "X", "name": "ProcessMessages 1165", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698654, "dur": 26, "ph": "X", "name": "ReadAsync 1165", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698683, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698698, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698700, "dur": 15, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698718, "dur": 14, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698735, "dur": 48, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698786, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698817, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698820, "dur": 16, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698838, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698840, "dur": 11, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698854, "dur": 24, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698881, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698900, "dur": 12, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698915, "dur": 13, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698932, "dur": 31, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698967, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698983, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********698985, "dur": 14, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699002, "dur": 14, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699019, "dur": 19, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699042, "dur": 14, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699059, "dur": 12, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699074, "dur": 12, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699090, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699132, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699151, "dur": 13, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699167, "dur": 15, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699186, "dur": 33, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699222, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699239, "dur": 16, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699259, "dur": 36, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699298, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699315, "dur": 14, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699332, "dur": 11, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699346, "dur": 29, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699378, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699397, "dur": 14, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699414, "dur": 10, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699427, "dur": 27, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699456, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699474, "dur": 14, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699491, "dur": 13, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699508, "dur": 12, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699523, "dur": 18, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699545, "dur": 11, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699559, "dur": 39, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699601, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699616, "dur": 29, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699648, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699666, "dur": 15, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699684, "dur": 12, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699699, "dur": 26, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699728, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699745, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699747, "dur": 13, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699763, "dur": 13, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699779, "dur": 16, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699798, "dur": 15, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699816, "dur": 10, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699829, "dur": 11, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699844, "dur": 28, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699876, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699894, "dur": 42, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699940, "dur": 14, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699957, "dur": 20, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699980, "dur": 15, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699997, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********699999, "dur": 12, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700013, "dur": 12, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700029, "dur": 30, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700062, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700082, "dur": 14, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700097, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700099, "dur": 15, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700118, "dur": 13, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700135, "dur": 15, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700152, "dur": 1, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700154, "dur": 12, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700169, "dur": 12, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700184, "dur": 31, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700218, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700235, "dur": 14, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700252, "dur": 16, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700270, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700272, "dur": 13, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700286, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700288, "dur": 13, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700304, "dur": 10, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700317, "dur": 13, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700333, "dur": 34, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700370, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700388, "dur": 14, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700404, "dur": 13, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700419, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700421, "dur": 14, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700438, "dur": 14, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700455, "dur": 10, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700468, "dur": 12, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700483, "dur": 35, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700520, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700535, "dur": 120, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700660, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700663, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********700686, "dur": 326, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701017, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701078, "dur": 7, "ph": "X", "name": "ProcessMessages 1180", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701087, "dur": 25, "ph": "X", "name": "ReadAsync 1180", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701114, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701116, "dur": 24, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701148, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701153, "dur": 32, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701188, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701191, "dur": 28, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701226, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701236, "dur": 38, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701275, "dur": 2, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701279, "dur": 19, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701299, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701301, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701324, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701349, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701351, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701381, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701384, "dur": 37, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701426, "dur": 3, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701431, "dur": 46, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701483, "dur": 4, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701490, "dur": 44, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701538, "dur": 3, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701543, "dur": 31, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701576, "dur": 2, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701580, "dur": 28, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701610, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701613, "dur": 59, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701674, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701677, "dur": 21, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701703, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701708, "dur": 33, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701743, "dur": 2, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701746, "dur": 31, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701784, "dur": 3, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701790, "dur": 33, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701827, "dur": 3, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701832, "dur": 31, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701867, "dur": 3, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701871, "dur": 29, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701904, "dur": 3, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701909, "dur": 36, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701952, "dur": 4, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701958, "dur": 26, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701987, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********701990, "dur": 21, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702013, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702016, "dur": 20, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702037, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702039, "dur": 56, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702102, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702108, "dur": 48, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702163, "dur": 5, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702172, "dur": 40, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702215, "dur": 4, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702220, "dur": 21, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702243, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702246, "dur": 25, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702274, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702277, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702308, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702313, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702339, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702341, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702387, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702391, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702429, "dur": 3, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702434, "dur": 71, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702511, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702516, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702560, "dur": 2, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702564, "dur": 27, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702595, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702599, "dur": 22, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702623, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702625, "dur": 17, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702644, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702646, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702673, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702675, "dur": 18, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702694, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702696, "dur": 23, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702726, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702731, "dur": 33, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702768, "dur": 2, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702772, "dur": 30, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702805, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702809, "dur": 27, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702844, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702850, "dur": 53, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702907, "dur": 3, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702913, "dur": 36, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702954, "dur": 3, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702960, "dur": 28, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702990, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********702993, "dur": 3981, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********706989, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********706996, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********707038, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********707042, "dur": 1698, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********708753, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********708760, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********708802, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********708806, "dur": 272, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********709088, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********709094, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********709126, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********709128, "dur": 4381, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********713524, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********713530, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********713565, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********713568, "dur": 61, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********713637, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********713642, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********713663, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********713665, "dur": 244, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********713913, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********713916, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********713956, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********713962, "dur": 491, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********714461, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********714466, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********714515, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********714520, "dur": 33, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********714555, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********714557, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********714585, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********714587, "dur": 58, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********714650, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********714655, "dur": 573, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********715232, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********715236, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********715262, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********715264, "dur": 75, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********715343, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********715366, "dur": 153, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********715524, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********715548, "dur": 120, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********715671, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********715673, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********715695, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********715698, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********715723, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********715728, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********715754, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********715775, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********715848, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********715869, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********715905, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********715927, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********715945, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********715947, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********715972, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********715992, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********716030, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********716049, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********716086, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********716090, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********716113, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********716116, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********716158, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********716176, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********716217, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********716243, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********716340, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********716346, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********716378, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********716401, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********716429, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********716432, "dur": 592, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717028, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717031, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717068, "dur": 3, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717072, "dur": 16, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717092, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717149, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717177, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717180, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717208, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717242, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717270, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717273, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717296, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717319, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717346, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717379, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717384, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717412, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717414, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717434, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717537, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717555, "dur": 79, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717637, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717655, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717682, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717685, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717713, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717731, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717733, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717755, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717758, "dur": 70, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********717832, "dur": 298, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718134, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718138, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718175, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718178, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718208, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718231, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718252, "dur": 126, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718384, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718388, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718426, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718431, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718463, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718466, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718543, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718548, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718578, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718581, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718603, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718605, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718635, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718707, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718736, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718741, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718796, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718798, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718830, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718834, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718862, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718895, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718901, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********718926, "dur": 114, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719042, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719048, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719079, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719084, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719115, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719117, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719142, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719145, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719181, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719186, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719214, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719218, "dur": 235, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719458, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719462, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719489, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719493, "dur": 151, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719648, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719671, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719709, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719713, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719749, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719754, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719790, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719794, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719826, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719829, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719864, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719867, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719900, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719903, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719933, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719937, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719965, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********719968, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720007, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720012, "dur": 116, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720134, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720138, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720182, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720186, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720206, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720209, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720240, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720266, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720269, "dur": 126, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720402, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720406, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720445, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720449, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720538, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720542, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720576, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720580, "dur": 79, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720667, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720672, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720706, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720710, "dur": 90, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720805, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720810, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********720846, "dur": 629, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********721484, "dur": 60, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********721552, "dur": 4, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********721560, "dur": 52, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********721619, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********721625, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********721674, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********721680, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********721738, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********721742, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********721786, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********721790, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********721825, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********721829, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********721864, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********721868, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********721905, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********721908, "dur": 206, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********722120, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********722126, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********722180, "dur": 8, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********722191, "dur": 126, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********722324, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********722328, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********722364, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********722367, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********722398, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********722401, "dur": 96, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********722505, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********722509, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********722530, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********722533, "dur": 145, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********722684, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********722688, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********722715, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********722718, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********722756, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********722759, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********722790, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********722795, "dur": 328, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********723127, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********723130, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********723183, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********723189, "dur": 86, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********723286, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********723291, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********723325, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********723328, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********723354, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********723356, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********723371, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********723373, "dur": 194, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********723571, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********723573, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********723588, "dur": 3, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********723592, "dur": 85, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********723685, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": **********723698, "dur": 373822, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748281097535, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748281097542, "dur": 178, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748281097722, "dur": 1512, "ph": "X", "name": "ProcessMessages 4374", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748281099242, "dur": 1712680, "ph": "X", "name": "ReadAsync 4374", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748282811935, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748282811941, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748282811992, "dur": 28, "ph": "X", "name": "ProcessMessages 8965", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748282812022, "dur": 26907, "ph": "X", "name": "ReadAsync 8965", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748282838938, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748282838944, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748282838995, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748282839000, "dur": 372, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748282839379, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748282839383, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748282839427, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748282839432, "dur": 118017, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748282957459, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748282957467, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748282957516, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748282957523, "dur": 279438, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283236972, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283236979, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283237062, "dur": 38, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283237104, "dur": 6972, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283244086, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283244092, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283244126, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283244132, "dur": 2024, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283246169, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283246175, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283246223, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283246253, "dur": 31792, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283278059, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283278067, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283278114, "dur": 39, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283278156, "dur": 3877, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283282044, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283282051, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283282142, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283282145, "dur": 27675, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283309835, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283309843, "dur": 106, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283309961, "dur": 46, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283310011, "dur": 3620, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283313644, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283313652, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283313730, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283313738, "dur": 71965, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283385714, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283385721, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283385793, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283385800, "dur": 2307, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283388118, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283388123, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283388175, "dur": 29, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283388207, "dur": 149229, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283537447, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283537454, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283537497, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283537501, "dur": 22336, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283559848, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283559855, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283559904, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283559909, "dur": 591, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283560507, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283560512, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283560535, "dur": 463, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 10784, "tid": 12884901888, "ts": 1750748283561004, "dur": 13700, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 10784, "tid": 9659, "ts": 1750748283586020, "dur": 3510, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 10784, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 10784, "tid": 8589934592, "ts": **********647191, "dur": 485187, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 10784, "tid": 8589934592, "ts": 1750748281132381, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 10784, "tid": 8589934592, "ts": 1750748281132388, "dur": 734, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 10784, "tid": 9659, "ts": 1750748283589533, "dur": 12, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 10784, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 10784, "tid": 4294967296, "ts": **********515662, "dur": 3060364, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 10784, "tid": 4294967296, "ts": **********518545, "dur": 109635, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 10784, "tid": 4294967296, "ts": 1750748283576097, "dur": 4614, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 10784, "tid": 4294967296, "ts": 1750748283578639, "dur": 33, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 10784, "tid": 4294967296, "ts": 1750748283580770, "dur": 12, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 10784, "tid": 9659, "ts": 1750748283589548, "dur": 12, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": **********677211, "dur": 1902, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********679124, "dur": 1669, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********680904, "dur": 132, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": **********681036, "dur": 738, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********682375, "dur": 103, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_A75BF8852D2D0B75.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": **********682864, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_25B12309E6316048.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": **********683356, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": **********690134, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": **********697398, "dur": 124, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15875789592854681416.rsp"}}, {"pid": 12345, "tid": 0, "ts": **********681792, "dur": 18993, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********700802, "dur": 2859334, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750748283560138, "dur": 126, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750748283560264, "dur": 53, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750748283560322, "dur": 123, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750748283560671, "dur": 53, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750748283560745, "dur": 7440, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": **********681577, "dur": 19233, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********700836, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********700994, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********701091, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********701191, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********701518, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": **********701676, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********701771, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********701877, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.MemoryProfiler.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": **********702395, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 1, "ts": **********702531, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********702679, "dur": 253, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": **********702933, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10927284454210886603.rsp"}}, {"pid": 12345, "tid": 1, "ts": **********703065, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6581075648720537469.rsp"}}, {"pid": 12345, "tid": 1, "ts": **********703141, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6581075648720537469.rsp"}}, {"pid": 12345, "tid": 1, "ts": **********703233, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********703618, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********704088, "dur": 842, "ph": "X", "name": "File", "args": {"detail": "Packages\\com.singularitygroup.hotreload\\Editor\\Helpers\\UnitySettingsHelper.cs"}}, {"pid": 12345, "tid": 1, "ts": **********703933, "dur": 1028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********704961, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********705152, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********705443, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********706026, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********706541, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********706768, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********707543, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********707973, "dur": 1137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********709110, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********709532, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********710362, "dur": 1321, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Widgets\\StickyNote\\StickyNoteWidget.cs"}}, {"pid": 12345, "tid": 1, "ts": **********709934, "dur": 1810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********711744, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********712420, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********712757, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********713499, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********713773, "dur": 625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********714403, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/YooAsset.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********714706, "dur": 1467, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********716177, "dur": 1017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/YooAsset.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********717195, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********717253, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/SingularityGroup.HotReload.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********717494, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UIEffect-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********717802, "dur": 955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UIEffect-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********718758, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********719065, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********719178, "dur": 804, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********720031, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********720277, "dur": 1605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********721883, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********721942, "dur": 1486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********723437, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********723541, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********723873, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********723939, "dur": 178560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********902500, "dur": 1345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": **********903846, "dur": 626, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********904477, "dur": 2429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": **********906907, "dur": 1937, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********908850, "dur": 4147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": **********912998, "dur": 3190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********916313, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********916367, "dur": 217007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750748281134554, "dur": 91, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1750748281134645, "dur": 463, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 1, "ts": 1750748281133375, "dur": 1756, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750748281135131, "dur": 2425039, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********681632, "dur": 19205, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********700842, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_77012D7F45F75688.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********701236, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_B8655BD4881E8EE6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********701352, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********701547, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********701693, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": **********701774, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": **********701860, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": **********701983, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********702146, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********702333, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********702526, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********702673, "dur": 199, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": **********703030, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********703138, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15077550221887618622.rsp"}}, {"pid": 12345, "tid": 2, "ts": **********703199, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********703896, "dur": 966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********704862, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********705212, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********705520, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********706178, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********706681, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********707146, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********707899, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********708694, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********709164, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********709487, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********710350, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********710904, "dur": 819, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections\\Unity.Collections\\FixedStringParseMethods.cs"}}, {"pid": 12345, "tid": 2, "ts": **********710723, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********711743, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********712386, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********713434, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********713759, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********714249, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/HybridCLR.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********714475, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********714582, "dur": 1544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/HybridCLR.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********716127, "dur": 2140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********718272, "dur": 766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/HybridCLR.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********719039, "dur": 1122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********720165, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********720329, "dur": 1567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********721897, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********721955, "dur": 1502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********723457, "dur": 177614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********901072, "dur": 1364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": **********902437, "dur": 2436, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********904878, "dur": 4160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": **********909039, "dur": 1556, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********910599, "dur": 3529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.MemoryProfiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": **********914128, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********914215, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********914573, "dur": 1326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********915939, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********916156, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********916282, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********916470, "dur": 2643676, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********681596, "dur": 19227, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********700836, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********701226, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_975DB55DEAAED482.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********701310, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_975DB55DEAAED482.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********701475, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********701658, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": **********701753, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********701811, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": **********701917, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": **********702192, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********702361, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********702501, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********702654, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********702991, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8301606844404808831.rsp"}}, {"pid": 12345, "tid": 3, "ts": **********703045, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********703184, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********703558, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********703899, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********704379, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********705054, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********705382, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********705982, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********706560, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********706965, "dur": 944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********707909, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********708114, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********709068, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********709878, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********710475, "dur": 1225, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\PackageEventListener.cs"}}, {"pid": 12345, "tid": 3, "ts": **********710282, "dur": 1646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********711928, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********712352, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********712619, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********713247, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********713772, "dur": 451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********714224, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********714441, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********714607, "dur": 708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********715315, "dur": 2214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********717535, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Utilities.ref.dll_E95AACB3AB3AB34A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********717996, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********718086, "dur": 1085, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********719171, "dur": 817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********719988, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********720275, "dur": 1603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********721878, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********721938, "dur": 1495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********723433, "dur": 179030, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********902465, "dur": 1639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Mosframe.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********904105, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********904170, "dur": 3146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********907317, "dur": 1094, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********908417, "dur": 1790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********910208, "dur": 3230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********913443, "dur": 3266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********916710, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********916795, "dur": 2643365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********682043, "dur": 19185, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********701240, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_1DAAD3ADBE6389DE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********701737, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 4, "ts": **********702106, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ScriptableBuildPipeline.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": **********702316, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********702500, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Coffee.UIParticle.rsp"}}, {"pid": 12345, "tid": 4, "ts": **********702592, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********702965, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********703124, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17654094715076075116.rsp"}}, {"pid": 12345, "tid": 4, "ts": **********703184, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********703441, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********703729, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********704031, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********704369, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********705521, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********706506, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********706791, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********707322, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********708316, "dur": 511, "ph": "X", "name": "File", "args": {"detail": "Packages\\com.unity.render-pipelines.core\\Runtime\\RenderPipeline\\IVolumetricCloud.cs"}}, {"pid": 12345, "tid": 4, "ts": **********708304, "dur": 966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********709271, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********709830, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********710564, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********710769, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********710978, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********711202, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********711442, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********711889, "dur": 1379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********713268, "dur": 530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********713798, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********714233, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.MemoryProfiler.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********714485, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********714804, "dur": 1460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.MemoryProfiler.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********716265, "dur": 489, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********716786, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/FancyScrollView.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********717000, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********717298, "dur": 1195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/FancyScrollView.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********718494, "dur": 547, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********719045, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********719168, "dur": 825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********719993, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********720274, "dur": 1614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********721934, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Game.Hotfix.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********722050, "dur": 1393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********723444, "dur": 179056, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********902506, "dur": 3497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********906004, "dur": 1036, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********907046, "dur": 3300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********910347, "dur": 1077, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********911428, "dur": 5005, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********916492, "dur": 2643699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********682090, "dur": 19155, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********701252, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_6DF54DD596B6ED4D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********701578, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********701838, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": **********702013, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.rsp"}}, {"pid": 12345, "tid": 5, "ts": **********702340, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********702535, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********702891, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/SingularityGroup.HotReload.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": **********703036, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3925285597375108267.rsp"}}, {"pid": 12345, "tid": 5, "ts": **********703120, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********703573, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********703963, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********704388, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********705160, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********705656, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********706179, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********706824, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********707304, "dur": 1025, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********708329, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********709103, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********709708, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********710584, "dur": 1135, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Dependencies\\ReorderableList\\ElementAdderMenu\\ElementAdderMeta.cs"}}, {"pid": 12345, "tid": 5, "ts": **********710496, "dur": 1595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********712092, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********712880, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********713372, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********713779, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********714245, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********714912, "dur": 1708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": **********716620, "dur": 454, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********717079, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********717543, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********717634, "dur": 1440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": **********719163, "dur": 727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********719891, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********720074, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": **********720585, "dur": 491, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********721103, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********721211, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": **********721569, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********721701, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********721779, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": **********722011, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********722124, "dur": 1326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********723450, "dur": 177629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********901081, "dur": 3093, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/HybridCLR.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": **********904175, "dur": 1514, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********905695, "dur": 4600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/IngameDebugConsole.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": **********910296, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********910397, "dur": 5875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": **********916273, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********916371, "dur": 1901167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750748282817551, "dur": 138524, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Game.Hotfix.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750748282817540, "dur": 139284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Game.Hotfix.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750748282957648, "dur": 98, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750748282959051, "dur": 278133, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Game.Hotfix.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750748283242426, "dur": 143472, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Game.Hotfix.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750748283242420, "dur": 143480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Game.Hotfix.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750748283385916, "dur": 2432, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Game.Hotfix.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750748283388354, "dur": 171800, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********682272, "dur": 19067, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********701340, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_7A8B9BFC3C3FAA80.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********701424, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********701630, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": **********701782, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 6, "ts": **********701900, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": **********702122, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********702188, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": **********702275, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********702506, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********702687, "dur": 228, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 6, "ts": **********702916, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16545194796390384707.rsp"}}, {"pid": 12345, "tid": 6, "ts": **********703167, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********703533, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********703791, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********704218, "dur": 970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********705189, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********705558, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********706469, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********706803, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********707232, "dur": 1538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********708770, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********709212, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********710299, "dur": 1343, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqCore\\Changelog_1_4_9.cs"}}, {"pid": 12345, "tid": 6, "ts": **********710171, "dur": 1958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********712129, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********712939, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********713497, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********713782, "dur": 628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********714411, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********714675, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********715051, "dur": 873, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": **********715925, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********716020, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Coffee.UIParticle.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********716369, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********716571, "dur": 1198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Coffee.UIParticle.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": **********717769, "dur": 1098, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********718870, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Coffee.UIParticle.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": **********719206, "dur": 1033, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********720269, "dur": 1603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********721887, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********722035, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": **********722377, "dur": 1059, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********723436, "dur": 179116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********902553, "dur": 4728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": **********907282, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********907349, "dur": 4045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": **********911395, "dur": 2866, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********914386, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********914605, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********914697, "dur": 1352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********916084, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********916290, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********916527, "dur": 2643653, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********681754, "dur": 19095, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********700857, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_78B89FCDC6642218.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********701101, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********701232, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_CA1C786273E921E0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********701364, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********701619, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********701734, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": **********702048, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********702441, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********702971, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1596698183737856109.rsp"}}, {"pid": 12345, "tid": 7, "ts": **********703174, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********703492, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********703998, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********704375, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********705257, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********705640, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********705980, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********706619, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********707516, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********707789, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********708871, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********709253, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********709633, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********709996, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********710560, "dur": 1151, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Inspection\\Primitives\\IntInspector.cs"}}, {"pid": 12345, "tid": 7, "ts": **********710407, "dur": 2556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********713040, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********713332, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********713760, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********714223, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********714652, "dur": 1913, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********716570, "dur": 982, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": **********717553, "dur": 736, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********718320, "dur": 838, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********719160, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********719388, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": **********719991, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********720082, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********720271, "dur": 1599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********721871, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********721977, "dur": 848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": **********722826, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********722949, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********723028, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": **********723441, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********723527, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": **********723727, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********723829, "dur": 178141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********901971, "dur": 4248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": **********906220, "dur": 5051, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********911277, "dur": 5049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": **********916327, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********916441, "dur": 2643677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********681773, "dur": 19086, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********700867, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_33CE7EEC29BAD477.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********701215, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********701323, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********701531, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": **********701681, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********701893, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Profiling.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": **********702108, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": **********702167, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********702387, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********702510, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********702682, "dur": 313, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/SingularityGroup.HotReload.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": **********703188, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********703544, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********704448, "dur": 521, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.tuyoogame.yooasset\\Editor\\AssetBundleBuilder\\BuildPipeline\\EditorSimulateBuildPipeline\\BuildTasks\\TaskGetBuildMap_ESBP.cs"}}, {"pid": 12345, "tid": 8, "ts": **********703888, "dur": 1157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********705045, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********705364, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********706234, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********706773, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********707178, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********707714, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********708441, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********709232, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********709812, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********710375, "dur": 1303, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Reflection\\TypeOptionTree.cs"}}, {"pid": 12345, "tid": 8, "ts": **********710095, "dur": 1614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********711710, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********712359, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********713477, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********713784, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********714237, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********714828, "dur": 1063, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********715891, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********715984, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********716228, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********716492, "dur": 830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********717322, "dur": 567, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********717897, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********717996, "dur": 1165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********719167, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********719405, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********719882, "dur": 896, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********720808, "dur": 1072, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********721880, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********721944, "dur": 1496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********723440, "dur": 179057, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********902498, "dur": 3896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": **********906395, "dur": 4100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********910500, "dur": 4267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": **********914768, "dur": 1131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********916237, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********916319, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********916800, "dur": 2643378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********681817, "dur": 19065, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********700924, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********701052, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********701234, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_48FCA722B5EE0F32.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********701315, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********701715, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 9, "ts": **********701889, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********702066, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********702355, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********702532, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********702792, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********702985, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18430889845441911807.rsp"}}, {"pid": 12345, "tid": 9, "ts": **********703162, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********703763, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********704170, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********705455, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********706301, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********706767, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********707341, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********707793, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********708594, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********709573, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********710485, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********710816, "dur": 906, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections\\Unity.Collections\\DebugView.cs"}}, {"pid": 12345, "tid": 9, "ts": **********710724, "dur": 1433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********712158, "dur": 1130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********713289, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********713770, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********714229, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********714629, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********714808, "dur": 1699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": **********716508, "dur": 889, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********717403, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ConversionSystem.ref.dll_DF4EB219DB1F4267.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********717560, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Coffee.UIParticle.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********717867, "dur": 828, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********718698, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********719158, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********719310, "dur": 812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": **********720123, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********720266, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********720474, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": **********721131, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********721218, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": **********721655, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********721889, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********722016, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": **********722281, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********722392, "dur": 1059, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********723451, "dur": 177631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********901083, "dur": 4965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": **********906048, "dur": 377, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********906431, "dur": 4345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": **********910777, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********911075, "dur": 3664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": **********914740, "dur": 1559, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********916335, "dur": 623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********916968, "dur": 2643172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********681835, "dur": 19059, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********700995, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_F7C6DE9D130C0B70.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********701236, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_2281B71902E16F2A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********701532, "dur": 279, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_2A790542FCF7EA37.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********701836, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": **********702095, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********702248, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********702444, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********702695, "dur": 194, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": **********702973, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********703160, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********703714, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********704022, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********704296, "dur": 1331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********705628, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********706041, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********706678, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********707246, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********707514, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********708280, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********708814, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********710359, "dur": 1307, "ph": "X", "name": "File", "args": {"detail": "Assets\\ThirdParty\\GameFramework\\Libraries\\GameFramework\\ObjectPool\\IObjectPool.cs"}}, {"pid": 12345, "tid": 10, "ts": **********709343, "dur": 2342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********711685, "dur": 1263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********712977, "dur": 100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********713077, "dur": 73, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********713150, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********713314, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********713765, "dur": 473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********714239, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********714610, "dur": 1260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": **********715871, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********716017, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********716650, "dur": 1514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": **********718165, "dur": 631, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********718801, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********718855, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********719175, "dur": 814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********719989, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********720262, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********720484, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": **********721031, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********721093, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********721269, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********721946, "dur": 1481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********723428, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********723613, "dur": 177462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********901077, "dur": 1356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-csharp.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": **********902434, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********902520, "dur": 3074, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": **********905594, "dur": 873, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********906473, "dur": 4015, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": **********910489, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********910604, "dur": 3265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": **********913870, "dur": 705, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********914673, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********914815, "dur": 1019, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/HybridCLR.Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": **********915954, "dur": 411, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********916369, "dur": 218765, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750748281135135, "dur": 2425006, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********681879, "dur": 19029, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********701063, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********701332, "dur": 646, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_F40B729AF437AB0F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********701996, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********702142, "dur": 6767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": **********709030, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********709315, "dur": 4345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": **********713662, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********713797, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********713879, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": **********714231, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********714530, "dur": 1381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": **********715911, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********715982, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********716201, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********716440, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********716653, "dur": 1595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": **********718248, "dur": 837, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********719165, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": **********719370, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": **********719874, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********719983, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 11, "ts": **********720533, "dur": 183, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********721065, "dur": 178759, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 11, "ts": **********901062, "dur": 1375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": **********902438, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********902547, "dur": 4454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": **********907002, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********907109, "dur": 4284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": **********911394, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********911498, "dur": 4447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": **********915946, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********916250, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": **********916388, "dur": 2326956, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750748283243367, "dur": 907, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Game.Hotfix.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1750748283243346, "dur": 929, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Game.Hotfix.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1750748283244300, "dur": 2036, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Game.Hotfix.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1750748283246342, "dur": 313814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********681902, "dur": 19013, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********701007, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_A75BF8852D2D0B75.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": **********701225, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_4A1FE717CA7BAC19.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": **********701290, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********701786, "dur": 496, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": **********702325, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********702494, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********702750, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": **********702976, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10266345212867571173.rsp"}}, {"pid": 12345, "tid": 12, "ts": **********703061, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7908171937365489455.rsp"}}, {"pid": 12345, "tid": 12, "ts": **********703140, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7908171937365489455.rsp"}}, {"pid": 12345, "tid": 12, "ts": **********703218, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********703777, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********704023, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********704388, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********705206, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********705580, "dur": 126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********705706, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********706089, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********706728, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********707676, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********707825, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********708007, "dur": 1368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********709375, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********710256, "dur": 1370, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Collections\\CountItems.cs"}}, {"pid": 12345, "tid": 12, "ts": **********709812, "dur": 1872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********711685, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********712374, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********712927, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********713502, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********713768, "dur": 625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********714402, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Mosframe.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": **********714812, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": **********715082, "dur": 680, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********715771, "dur": 1238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": **********717010, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********717257, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": **********717606, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": **********718144, "dur": 804, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********718951, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********719212, "dur": 771, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********719983, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********720267, "dur": 1609, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********721876, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********721933, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Game.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": **********722096, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Game.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": **********722529, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Game.Hotfix.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": **********723642, "dur": 176, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": **********725248, "dur": 2087798, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/Game.Hotfix.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750748282817772, "dur": 21257, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Game.Hotfix.ref.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750748282817535, "dur": 21579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750748282839160, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750748282839780, "dur": 116294, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Game.Hotfix.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750748282839620, "dur": 117116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750748282957306, "dur": 96, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750748282959028, "dur": 319238, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750748283282134, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1750748283282127, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1750748283282238, "dur": 277907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********681924, "dur": 19018, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********701151, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********701323, "dur": 532, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_25B12309E6316048.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": **********701952, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": **********702136, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********702518, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********702691, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.Tests.rsp2"}}, {"pid": 12345, "tid": 13, "ts": **********702748, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.rsp"}}, {"pid": 12345, "tid": 13, "ts": **********702934, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********703107, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********703488, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********703806, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********704227, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********705539, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********706348, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********706549, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********707017, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********707601, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********708095, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********708326, "dur": 106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********708432, "dur": 100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********708532, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********708794, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********709459, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********710412, "dur": 1274, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Control\\While.cs"}}, {"pid": 12345, "tid": 13, "ts": **********709720, "dur": 2574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********712295, "dur": 963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********713258, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********713796, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********714249, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UIEffect.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": **********714672, "dur": 858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UIEffect.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": **********715530, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********715609, "dur": 999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": **********716608, "dur": 382, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********716993, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": **********717226, "dur": 1217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": **********718444, "dur": 640, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********719117, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": **********719304, "dur": 563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": **********719867, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********719987, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********720260, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": **********720489, "dur": 684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": **********721174, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********721293, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": **********721384, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": **********721941, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": **********722112, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": **********722514, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********722616, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": **********722746, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": **********722950, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********723009, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********723445, "dur": 177618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********901092, "dur": 3951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Coffee.UIParticle.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": **********905044, "dur": 872, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********905922, "dur": 2869, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityGameFramework.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": **********908791, "dur": 1616, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********910415, "dur": 5691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ScriptableBuildPipeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": **********916107, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********916279, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": **********916598, "dur": 2643536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********681938, "dur": 19081, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********701049, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********701314, "dur": 310, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_0E5FF1AB563A0ED2.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": **********701870, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": **********701984, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********702131, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********702281, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********702507, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********702654, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********702923, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********703170, "dur": 381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********703551, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********704115, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********704411, "dur": 1057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********705468, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********706336, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********706989, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********707530, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********707861, "dur": 529, "ph": "X", "name": "File", "args": {"detail": "Packages\\com.unity.render-pipelines.core\\Runtime\\GPUDriven\\InstanceData\\InstanceData.cs"}}, {"pid": 12345, "tid": 14, "ts": **********708397, "dur": 580, "ph": "X", "name": "File", "args": {"detail": "Packages\\com.unity.render-pipelines.core\\Runtime\\GPUDriven\\InstanceData\\GPUInstanceDataUploader.cs"}}, {"pid": 12345, "tid": 14, "ts": **********707833, "dur": 1405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********709238, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********709688, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********710328, "dur": 1329, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Plugin\\BoltCoreMigration.cs"}}, {"pid": 12345, "tid": 14, "ts": **********710278, "dur": 2086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********712365, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********712822, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********712872, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********713426, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********713775, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********714247, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": **********714569, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********714899, "dur": 1155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": **********716103, "dur": 961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": **********717065, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********717166, "dur": 1929, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/AmplifyShaderEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": **********719095, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********719348, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********720015, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********720417, "dur": 1478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********721942, "dur": 1483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********723426, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": **********723611, "dur": 177522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********901134, "dur": 4754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UIEffect.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": **********905889, "dur": 652, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********906546, "dur": 4488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": **********911035, "dur": 577, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": **********911617, "dur": 5052, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": **********916713, "dur": 2643471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********681992, "dur": 19120, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********701154, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********701610, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********701680, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********701774, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": **********702099, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********702507, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********702653, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********702717, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********702867, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********703125, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********703202, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********703750, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********704236, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********705041, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********705586, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********705815, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********706334, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********706593, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********707146, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********707630, "dur": 1380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********709034, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********709357, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********709674, "dur": 949, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********710927, "dur": 803, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections\\Unity.Collections\\NativeQueue.cs"}}, {"pid": 12345, "tid": 15, "ts": **********710624, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********711775, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********712356, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********712709, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********713225, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********713777, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********714231, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": **********714453, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********714771, "dur": 1245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": **********716016, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********716367, "dur": 836, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/IngameDebugConsole.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": **********717204, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********717282, "dur": 906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/IngameDebugConsole.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": **********718189, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********718324, "dur": 850, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********719174, "dur": 810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********719984, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********720266, "dur": 1609, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********721875, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********721940, "dur": 1495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********723435, "dur": 177654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********901090, "dur": 2363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": **********903454, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********903553, "dur": 2109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": **********905663, "dur": 428, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********906095, "dur": 3597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Sirenix.OdinInspector.Modules.UnityMathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": **********909693, "dur": 568, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********910267, "dur": 3723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/FancyScrollView.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": **********913991, "dur": 530, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********914531, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********914801, "dur": 1339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********916159, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": **********916375, "dur": 1922788, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750748282839564, "dur": 116489, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Game.Hotfix.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750748282839166, "dur": 117570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750748282957489, "dur": 100, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750748282959038, "dur": 350997, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750748283313729, "dur": 223900, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750748283313723, "dur": 223908, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750748283537654, "dur": 22463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********682008, "dur": 19109, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********701224, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********701309, "dur": 425, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_8622EB2AD063DE6D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": **********701903, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 16, "ts": **********702071, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UIEffect.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": **********702261, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********702378, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********702681, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/IngameDebugConsole.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": **********702835, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14434236700012358782.rsp"}}, {"pid": 12345, "tid": 16, "ts": **********703063, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4571106892069148322.rsp"}}, {"pid": 12345, "tid": 16, "ts": **********703128, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********703212, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********703947, "dur": 937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********704884, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********705164, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********705449, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********706183, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********706630, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********707295, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********707475, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********707840, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********708515, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********708837, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********709449, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********709945, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********710518, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********710778, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********710994, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********711253, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********711475, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********711898, "dur": 1115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********713013, "dur": 60, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********713073, "dur": 128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********713228, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********713762, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********714254, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": **********714559, "dur": 2167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": **********716726, "dur": 904, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********717680, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": **********717927, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********717989, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": **********718691, "dur": 431, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********719165, "dur": 829, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********719994, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********720266, "dur": 1607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********721874, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********721939, "dur": 1492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********723432, "dur": 177629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********901063, "dur": 2029, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": **********903093, "dur": 3196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********906293, "dur": 3075, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityGameFramework.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": **********909369, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********909442, "dur": 3931, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UIEffect-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": **********913374, "dur": 323, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": **********913702, "dur": 3221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/IngameDebugConsole.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": **********916964, "dur": 2643186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********681624, "dur": 19206, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********700837, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_94D2176B11FDF36E.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": **********701063, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********701234, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********701294, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_469B342E21A8D2C6.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": **********701563, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": **********702033, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********702360, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********702461, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": **********702517, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********702810, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********703114, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********703199, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********703445, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********703964, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********704851, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********705203, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********705595, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********705880, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********706404, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********706803, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********707535, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********708847, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********709566, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********710454, "dur": 1234, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Interface\\Colors\\ColorPalette.cs"}}, {"pid": 12345, "tid": 17, "ts": **********710323, "dur": 2011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********712335, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********713115, "dur": 60, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********713239, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********713773, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********714219, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": **********714402, "dur": 960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": **********715363, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********715478, "dur": 446, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********715931, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": **********716229, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": **********716855, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********717153, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": **********717455, "dur": 1384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": **********718840, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********719161, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.MemoryProfiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": **********719267, "dur": 457, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********719726, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.MemoryProfiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": **********720331, "dur": 703, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********721072, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": **********721214, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": **********721606, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********721684, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********721901, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********722007, "dur": 1455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********723462, "dur": 179002, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********902470, "dur": 4198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/SingularityGroup.HotReload.Runtime.Public.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": **********906717, "dur": 3857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": **********910574, "dur": 808, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********911388, "dur": 2848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": **********914237, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********914294, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********914396, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********914654, "dur": 1486, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********916268, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": **********916418, "dur": 2397308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750748283313745, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1750748283313728, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1750748283313843, "dur": 246315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********682057, "dur": 19183, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********701243, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_6D0654A6A14E91C5.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": **********701459, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********701592, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********701795, "dur": 260, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 18, "ts": **********702057, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********702416, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********702675, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": **********702882, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********703060, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********703165, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********703492, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********703720, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********704077, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********704307, "dur": 520, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.tuyoogame.yooasset\\Runtime\\FileSystem\\DefaultWebRemoteFileSystem\\Operation\\DWRFSInitializeOperation.cs"}}, {"pid": 12345, "tid": 18, "ts": **********704307, "dur": 1255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********705562, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********706519, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********706742, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********707394, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********707853, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********708676, "dur": 803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********709480, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********709761, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********710371, "dur": 1322, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqCore\\Changelog_1_2_2.cs"}}, {"pid": 12345, "tid": 18, "ts": **********710249, "dur": 2101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********712351, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********713547, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********713763, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********714232, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": **********714770, "dur": 897, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": **********715668, "dur": 2307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********717989, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********718274, "dur": 890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********719164, "dur": 832, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********719996, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********720274, "dur": 1611, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********721935, "dur": 1502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********723437, "dur": 177622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********901060, "dur": 1375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": **********902436, "dur": 4484, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********906926, "dur": 2508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/SingularityGroup.HotReload.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": **********909435, "dur": 1484, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********910926, "dur": 3507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": **********914434, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********914490, "dur": 1374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********915875, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********916313, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": **********916732, "dur": 2643424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********681645, "dur": 19198, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********700849, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_BEE963F0D57B0FA6.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": **********701217, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********701298, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_080B249FD8388ADD.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": **********701520, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": **********701608, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********701809, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Profiling.Core.rsp2"}}, {"pid": 12345, "tid": 19, "ts": **********701956, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********702190, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Tests.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": **********702353, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********702511, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********702687, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 19, "ts": **********702806, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********703063, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********703197, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********703691, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********703964, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********705056, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********705450, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********705967, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********706480, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********707008, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********707462, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********708314, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********709028, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********709504, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********710289, "dur": 1348, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Interface\\Fuzzy\\FuzzyOptionTreeExtensionProvider.cs"}}, {"pid": 12345, "tid": 19, "ts": **********710289, "dur": 2095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********712385, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********713261, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********713758, "dur": 469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********714228, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": **********714569, "dur": 3785, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********718358, "dur": 623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": **********718981, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********719101, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": **********719334, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": **********719954, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********720052, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": **********720220, "dur": 630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": **********720850, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********720975, "dur": 918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********721893, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********721950, "dur": 1495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********723446, "dur": 179058, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********902505, "dur": 4825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": **********907331, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********907395, "dur": 2925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": **********910320, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********910399, "dur": 3803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity-editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": **********914203, "dur": 1660, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********916044, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********916285, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": **********916431, "dur": 2643684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********682102, "dur": 19149, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********701255, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_134F3A144AD95EA3.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": **********701636, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********702030, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********702127, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********702453, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********702690, "dur": 212, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Tests.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": **********703065, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********703172, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********703490, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********703801, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********704215, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********704411, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********704963, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********705438, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********706149, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********706641, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********707244, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********707859, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********708744, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********709091, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********709639, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********709984, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********710474, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********710857, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********711114, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********711313, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********711544, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********712210, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********712939, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********713433, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********713783, "dur": 617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********714405, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": **********714968, "dur": 742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": **********715711, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********715962, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": **********716158, "dur": 748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": **********716907, "dur": 1023, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********717941, "dur": 840, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********718785, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********719173, "dur": 814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********719988, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********720261, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": **********720491, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": **********720967, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********721065, "dur": 822, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********721888, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********721948, "dur": 1499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********723448, "dur": 179019, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********902468, "dur": 2846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ScriptableBuildPipeline.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": **********905315, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********905425, "dur": 2408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": **********907834, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********907897, "dur": 3474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": **********911372, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": **********911500, "dur": 2505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/HybridCLR.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": **********914054, "dur": 2720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": **********916843, "dur": 2643323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********682120, "dur": 19135, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********701258, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_BE0BBC97B2860369.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": **********701426, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********701557, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********701711, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 21, "ts": **********701879, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": **********701994, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": **********702110, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": **********702233, "dur": 4845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": **********707080, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********707232, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********707710, "dur": 108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********707818, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********708055, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********708527, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********709186, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********710274, "dur": 1354, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Physics\\OnTriggerEnter.cs"}}, {"pid": 12345, "tid": 21, "ts": **********709643, "dur": 2188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********711831, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********712773, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********712824, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********713421, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********713770, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********714251, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": **********714429, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********714535, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": **********714780, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": **********715437, "dur": 678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": **********716115, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********716287, "dur": 647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": **********716934, "dur": 492, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********717428, "dur": 840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": **********718269, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********718338, "dur": 827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********719166, "dur": 825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********719991, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********720276, "dur": 1605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********721881, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********721936, "dur": 1494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********723431, "dur": 177636, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********901068, "dur": 1364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": **********902433, "dur": 4610, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********907054, "dur": 3121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": **********910176, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********910259, "dur": 3805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": **********914065, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********914172, "dur": 2159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********916334, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": **********916856, "dur": 2643312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********682137, "dur": 19124, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********701264, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_0B623C68B67432F5.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": **********701374, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********701589, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********701709, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 22, "ts": **********701986, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/HybridCLR.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": **********702106, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Game.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": **********702188, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********702356, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********702491, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********702758, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********702971, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********703114, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********703549, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********703861, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********704470, "dur": 501, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.tuyoogame.yooasset\\Runtime\\FileSystem\\DefaultBuildinFileSystem\\Operation\\DBFSLoadPackageManifestOperation.cs"}}, {"pid": 12345, "tid": 22, "ts": **********704366, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********705311, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********706148, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********706712, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********707280, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********707492, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********707951, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********708656, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********709103, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********709528, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********710412, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********710621, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********710865, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********711110, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********711320, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********711543, "dur": 1401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********712944, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********713257, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********713761, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********714221, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": **********714583, "dur": 2367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": **********716951, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********717050, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": **********717254, "dur": 2189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": **********719443, "dur": 409, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********719887, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": **********720039, "dur": 533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": **********720573, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********720644, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********720900, "dur": 1042, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********721943, "dur": 1499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********723442, "dur": 179093, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********902536, "dur": 3838, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": **********906374, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********906443, "dur": 4754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": **********911198, "dur": 3228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********914487, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********914795, "dur": 1063, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********916054, "dur": 658, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": **********916717, "dur": 2643399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********682152, "dur": 19114, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********701269, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_13B91445996F2B3C.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": **********701519, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": **********701670, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********701791, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": **********701920, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ScriptableBuildPipeline.rsp"}}, {"pid": 12345, "tid": 23, "ts": **********702154, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityGameFramework.Editor.rsp2"}}, {"pid": 12345, "tid": 23, "ts": **********702274, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********702379, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********702483, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Game.Hotfix.rsp"}}, {"pid": 12345, "tid": 23, "ts": **********702546, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********703175, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********703496, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********703891, "dur": 1172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********705063, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********705475, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********706089, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********706961, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********707384, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********708005, "dur": 1229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********709234, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********710260, "dur": 1328, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Plugin\\Migrations\\Migration_1_4_0_f5_to_1_4_0_f6.cs"}}, {"pid": 12345, "tid": 23, "ts": **********710164, "dur": 2176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********712340, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********713117, "dur": 70, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********713236, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********713781, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********714246, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityGameFramework.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": **********714829, "dur": 735, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Mosframe.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": **********715564, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********715967, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/SingularityGroup.HotReload.Runtime.Public.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": **********716219, "dur": 553, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********716773, "dur": 675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/SingularityGroup.HotReload.Runtime.Public.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": **********717448, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********717578, "dur": 913, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/SingularityGroup.HotReload.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": **********718491, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********718627, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/SingularityGroup.HotReload.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": **********719292, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********719394, "dur": 601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********719996, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********720263, "dur": 845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********721109, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": **********721292, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": **********721711, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********721829, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********721894, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********721947, "dur": 1487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********723434, "dur": 177646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********901088, "dur": 2953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/FancyScrollView.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": **********904042, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********904103, "dur": 3633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": **********907737, "dur": 2614, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********910355, "dur": 2656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": **********913011, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": **********913371, "dur": 3418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/AmplifyShaderEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": **********916839, "dur": 2643327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********682167, "dur": 19102, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********701274, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_FB5E147B1DA0D362.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": **********701336, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********701602, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********701718, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********701866, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": **********701937, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********702286, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/YooAsset.rsp"}}, {"pid": 12345, "tid": 24, "ts": **********702339, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********702452, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********702719, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********702971, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********703103, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********703585, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********703940, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********704422, "dur": 507, "ph": "X", "name": "File", "args": {"detail": "Assets\\ThirdParty\\UIEffect\\Common\\BaseMeshEffect.cs"}}, {"pid": 12345, "tid": 24, "ts": **********704376, "dur": 1217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********705593, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********705816, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********706217, "dur": 379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********706596, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********707233, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********707714, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********708153, "dur": 977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********709130, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********710311, "dur": 1336, "ph": "X", "name": "File", "args": {"detail": "Assets\\ThirdParty\\GameFramework\\Libraries\\GameFramework\\Base\\Log\\GameFrameworkLog.ILogHelper.cs"}}, {"pid": 12345, "tid": 24, "ts": **********709476, "dur": 2185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********711661, "dur": 995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********712656, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********713508, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********713771, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********714251, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/IngameDebugConsole.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": **********714619, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********714686, "dur": 1170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/IngameDebugConsole.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": **********715856, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********715922, "dur": 1736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": **********717658, "dur": 2710, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********720406, "dur": 1486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********721937, "dur": 1501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********723439, "dur": 177645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********901085, "dur": 3178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/YooAsset.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": **********904264, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********904484, "dur": 3769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": **********908254, "dur": 1472, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********909732, "dur": 3429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": **********913162, "dur": 1122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********914294, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********914474, "dur": 2220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": **********916697, "dur": 2643446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********682181, "dur": 19093, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********701309, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_52DC12C617F0F86A.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": **********701650, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 25, "ts": **********701745, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********701805, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 25, "ts": **********702069, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********702168, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********702353, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********702800, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17165269797377373369.rsp"}}, {"pid": 12345, "tid": 25, "ts": **********702989, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6699892698108865450.rsp"}}, {"pid": 12345, "tid": 25, "ts": **********703113, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********703503, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********703917, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********704393, "dur": 501, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.tuyoogame.yooasset\\Runtime\\OperationSystem\\EOperationStatus.cs"}}, {"pid": 12345, "tid": 25, "ts": **********704273, "dur": 972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********705246, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********705712, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********706069, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********706579, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********706936, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********707478, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********707713, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********708162, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********708445, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********709180, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********709560, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********710459, "dur": 1232, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Interface\\LudiqGUIUtility.cs"}}, {"pid": 12345, "tid": 25, "ts": **********710284, "dur": 1802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********712086, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********713006, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********713322, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********713767, "dur": 478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********714245, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/FancyScrollView.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": **********714558, "dur": 636, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********715197, "dur": 647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/FancyScrollView.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": **********715844, "dur": 625, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********716475, "dur": 664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/HybridCLR.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": **********717249, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********717320, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/SingularityGroup.HotReload.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": **********717796, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********717988, "dur": 399, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********718389, "dur": 777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********719167, "dur": 827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********719994, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********720258, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": **********720431, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": **********720869, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********720991, "dur": 898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********721889, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********722017, "dur": 1441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********723459, "dur": 177606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********901067, "dur": 1368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 25, "ts": **********902435, "dur": 1054, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********903496, "dur": 2873, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 25, "ts": **********906370, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********906447, "dur": 3922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Coffee.UIParticle.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 25, "ts": **********910369, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********910469, "dur": 2781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 25, "ts": **********913251, "dur": 943, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********914329, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********914653, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********914788, "dur": 1280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********916106, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": **********916409, "dur": 2365722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750748283282153, "dur": 277851, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750748283282132, "dur": 277878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 26, "ts": **********682213, "dur": 19067, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********701313, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_2F157D92740770BF.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": **********701523, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 26, "ts": **********701846, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/GameFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 26, "ts": **********702009, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 26, "ts": **********702061, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********702176, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********702412, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********702652, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********702812, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14194224405417392639.rsp"}}, {"pid": 12345, "tid": 26, "ts": **********702976, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********703166, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********703590, "dur": 133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********703723, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********704008, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********704454, "dur": 550, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.tuyoogame.yooasset\\Runtime\\FileSystem\\DefaultCacheFileSystem\\Operation\\internal\\DownloadPackageHashOperation.cs"}}, {"pid": 12345, "tid": 26, "ts": **********704359, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********705534, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********706374, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********706694, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********707111, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********707952, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********708329, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********709075, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********709442, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********709630, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********710208, "dur": 1438, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Plugins\\PluginMigration.cs"}}, {"pid": 12345, "tid": 26, "ts": **********710145, "dur": 2005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********712150, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********712570, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********713504, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********713766, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********714234, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ScriptableBuildPipeline.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": **********714574, "dur": 1835, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********716415, "dur": 1797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ScriptableBuildPipeline.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": **********718212, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********718271, "dur": 1022, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********719297, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ScriptableBuildPipeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": **********719424, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ScriptableBuildPipeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": **********719888, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********720117, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/YooAsset.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": **********720266, "dur": 865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/YooAsset.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": **********721132, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********721251, "dur": 647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********721938, "dur": 1503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********723441, "dur": 177631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********901084, "dur": 3820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.MemoryProfiler.dll (+pdb)"}}, {"pid": 12345, "tid": 26, "ts": **********904905, "dur": 729, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********905639, "dur": 4828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Game.dll (+pdb)"}}, {"pid": 12345, "tid": 26, "ts": **********910468, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********910823, "dur": 3600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 26, "ts": **********914424, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********914483, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********914554, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********914731, "dur": 1192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********916022, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********916084, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********916142, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********916252, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********916330, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": **********916854, "dur": 2643293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********682243, "dur": 19040, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********701314, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_13492F2AD26F374B.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": **********701396, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 27, "ts": **********701453, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********701563, "dur": 187, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 27, "ts": **********701929, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********702128, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********702229, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********702410, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 27, "ts": **********702515, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********702808, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14404472718305077545.rsp"}}, {"pid": 12345, "tid": 27, "ts": **********702977, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16876957367423690161.rsp"}}, {"pid": 12345, "tid": 27, "ts": **********703067, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********703160, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********703535, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********703849, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********704164, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********704926, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********705078, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********705420, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********706292, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********706854, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********707218, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********707741, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********708545, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********709316, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********710337, "dur": 1325, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Scalar\\ScalarPerSecond.cs"}}, {"pid": 12345, "tid": 27, "ts": **********709595, "dur": 2195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********711790, "dur": 985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********712824, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********713394, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********713797, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********714249, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/GameFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": **********714896, "dur": 1512, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********716413, "dur": 1992, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/GameFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": **********718406, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********718464, "dur": 856, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityGameFramework.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": **********719320, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********719450, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityGameFramework.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": **********719597, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********719703, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityGameFramework.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": **********720180, "dur": 751, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********720960, "dur": 924, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********721885, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********721940, "dur": 1515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********723455, "dur": 179046, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********902502, "dur": 1863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": **********904365, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********904431, "dur": 2612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/GameFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": **********907044, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********907391, "dur": 2402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": **********909794, "dur": 665, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********910464, "dur": 5507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": **********915972, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********916080, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********916191, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": **********916487, "dur": 2643626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********681793, "dur": 19076, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********701181, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********701458, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********701664, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********701784, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.EditorCoroutines.Editor.rsp2"}}, {"pid": 12345, "tid": 28, "ts": **********702006, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.MemoryProfiler.Editor.rsp"}}, {"pid": 12345, "tid": 28, "ts": **********702316, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********702536, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********702685, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 28, "ts": **********702864, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********703177, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********703886, "dur": 1083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********704970, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********705458, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********706114, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********706614, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********707541, "dur": 519, "ph": "X", "name": "File", "args": {"detail": "Packages\\com.unity.render-pipelines.core\\Tests\\Runtime\\DebugManagerTests.cs"}}, {"pid": 12345, "tid": 28, "ts": **********707326, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********708072, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********708703, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********709444, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********710383, "dur": 1289, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Products\\Product.cs"}}, {"pid": 12345, "tid": 28, "ts": **********710125, "dur": 1826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********711951, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********712509, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********713298, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********713774, "dur": 632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********714410, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": **********714869, "dur": 481, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********715353, "dur": 915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": **********716268, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********716513, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/AmplifyShaderEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": **********716706, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********716861, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": **********717047, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********717233, "dur": 1213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": **********718491, "dur": 684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********719175, "dur": 810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********719986, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********720265, "dur": 869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********721135, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": **********721285, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": **********721670, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********721790, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********721951, "dur": 1499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********723450, "dur": 177619, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********901070, "dur": 3089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 28, "ts": **********904160, "dur": 1325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********905490, "dur": 3154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/SingularityGroup.HotReload.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 28, "ts": **********908645, "dur": 367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********909017, "dur": 4068, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/YooAsset.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 28, "ts": **********913086, "dur": 3329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": **********916449, "dur": 2643708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750748283572976, "dur": 1531, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 10784, "tid": 9659, "ts": 1750748283590359, "dur": 22532, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 10784, "tid": 9659, "ts": 1750748283613071, "dur": 2316, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 10784, "tid": 9659, "ts": 1750748283584565, "dur": 32047, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}