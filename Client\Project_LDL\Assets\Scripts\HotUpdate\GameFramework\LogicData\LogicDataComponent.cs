using System;
using System.Collections.Generic;
using System.Linq;
using Game.Hotfix.Config;
using NUnit.Framework;
using Role;
using Roledata;
using Tech;
using UnityEngine;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public class LogicDataComponent : GameFrameworkComponent
    {
        public RoleData RoleData
        {
            get { return m_RoleData; }
        }

        public BuildingData BuildingData
        {
            get { return m_BuildingData; }
        }

        public UserData UserData
        {
            get { return m_UserData; }
        }

        public MainCityGridData GridData
        {
            get { return m_MainCityGridData; }
        }

        public MailData MailData
        {
            get { return m_MailData; }
        }

        public EquipmentData EquipmentData
        {
            get { return m_equipmentData; }
        }

        public RecruitData RecruitData
        {
            get { return m_RecruitData; }
        }

        public QueueData QueueData
        {
            get { return m_QueueData; }
        }

        public HeroData HeroData
        {
            get { return m_HeroData; }
        }
        public BagData BagData
        {
            get { return m_BagData; }
        }

        public PvePathData PvePathData
        {
            get { return m_PvePathData; }
        }

        public GeneralShopData GeneralShopData
        {
            get { return m_GeneralShopData; }
        }

        public UnionData UnionData
        {
            get { return m_UnionData; }
        }

        public SoldierData SoliderData
        {
            get { return m_SoldierData; }
        }

        public MallData MallData
        {
            get { return m_MallData; }
        }

        public Battle5v5Data Battle5v5Data
        {
            get { return m_Battle5V5Data; }
        }

        public PaymentData PaymentData
        {
            get { return m_paymentData; }
        }

        public MainCityAreaData MainCityAreaData
        {
            get { return m_MainCityAreaData; }
        }

        public TaskData TaskData
        {
            get { return m_TaskData; }
        }
         public TechData TechData
        {
            get { return m_TechData; }
        }
        public VipData VipData
        {
            get { return m_VipData; }
        }

        public WorldMapData WorldMapData
        {
            get { return m_WorldMapData; }
        }

        public SurvivorData SurvivorData
        {
            get { return m_SurvivorData; }
        }

        public NoviceTrainingData NoviceTrainingData
        {
            get { return m_NoviceTrainingData; }
        }

        public DungeonData DungeonData
        {
            get { return m_DungeonData; }
        }

        public ChaoZhiData ChaoZhiData
        {
            get { return m_ChaoZhiData; }
        }
        
        public UAVData UAVData
        {
            get { return m_UAVData; }
        }

        public PeakRankData PeakRankData
        {
            get { return m_PeakRankData; }
        }

        public TradeTruckData TradeTruckData
        {
            get { return m_tradeTruckData; }
        }
        
        public TeamData TeamData
        {
            get { return m_TeamData; }
        }      
        
        public PrivilegeData PrivilegeData
        {
            get { return m_privilegeData; }
        }

        private RoleData m_RoleData;
        private BuildingData m_BuildingData;
        private UserData m_UserData;
        private MainCityGridData m_MainCityGridData;//地块信息
        private MailData m_MailData;
        private EquipmentData m_equipmentData;
        private RecruitData m_RecruitData;
        private QueueData m_QueueData;
        private HeroData m_HeroData;
        private BagData m_BagData;
        private PvePathData m_PvePathData;//pvp 路径
        private float m_UpdatePerSecond;
        private GeneralShopData m_GeneralShopData;
        private UnionData m_UnionData;
        private SoldierData m_SoldierData;
        private MallData m_MallData;
        private Battle5v5Data m_Battle5V5Data;//5v5 战斗相关
        private PaymentData m_paymentData;
        private MainCityAreaData m_MainCityAreaData;
        private TaskData m_TaskData;
        private TechData m_TechData;
        private VipData m_VipData;
        private WorldMapData m_WorldMapData;
        private NoviceTrainingData m_NoviceTrainingData;
        private SurvivorData m_SurvivorData;
        private DungeonData m_DungeonData;
        private ChaoZhiData m_ChaoZhiData;
        private PeakRankData m_PeakRankData;
        private TradeTruckData m_tradeTruckData;
        private UAVData m_UAVData;
        private TeamData m_TeamData;
        private PrivilegeData m_privilegeData;

        protected override void Awake()
        {
            base.Awake();
            m_RoleData = new RoleData();
            m_MainCityGridData = new MainCityGridData();
            m_BuildingData = new BuildingData();
            m_UserData = new UserData();
            m_MailData = new MailData();
            m_equipmentData = new EquipmentData();
            m_RecruitData = new RecruitData();
            m_QueueData = new QueueData();
            m_HeroData = new HeroData();
            m_BagData = new BagData();
            m_PvePathData = new PvePathData();
            m_GeneralShopData = new GeneralShopData();
            m_UnionData = new();
            m_SoldierData = new SoldierData();
            m_MallData = new MallData();
            m_Battle5V5Data = new Battle5v5Data();
            m_paymentData = new PaymentData();
            m_MainCityAreaData = new MainCityAreaData();
            m_TaskData = new TaskData();
            m_TechData = new TechData();
            m_VipData = new VipData();
            m_WorldMapData = new WorldMapData();
            m_NoviceTrainingData = new NoviceTrainingData();
            m_SurvivorData = new SurvivorData();
            m_DungeonData = new DungeonData();
            m_ChaoZhiData = new ChaoZhiData();
            m_PeakRankData = new PeakRankData();
            m_tradeTruckData = new TradeTruckData();
            m_UAVData = new UAVData();
            m_TeamData = new TeamData();
            m_privilegeData = new PrivilegeData();
        }

        public void InitGameData(RoleInfo roleInfo)
        {
            m_MainCityGridData.Init();
            m_RoleData.Init(roleInfo);
            m_QueueData.Init(roleInfo?.Queue, roleInfo?.Workers);
            m_BuildingData.Init(roleInfo?.Build);
            m_MailData.Init();
            m_equipmentData.Init(roleInfo?.Equip);
            m_RecruitData.Init();
            m_HeroData.Init(roleInfo?.Hero);
            m_BagData.Init(roleInfo?.Item);
            m_PvePathData.Init(roleInfo?.Attr?.InnerCityGridId);
            m_GeneralShopData.Init();
            m_UnionData.Init(roleInfo?.Union);
            m_SoldierData.Init(roleInfo?.Soldiers, roleInfo?.Hospitals);
            m_MallData.Init();
            m_Battle5V5Data.Init();
            m_paymentData.Init();
            m_MainCityAreaData.Init(roleInfo?.Attr?.InnerCityRegionId);
            m_TaskData.Init(roleInfo?.Tasks);
            m_TechData.Init(roleInfo?.Techs);
            m_VipData.Init(roleInfo?.Attr);
            m_WorldMapData.Init();
            m_NoviceTrainingData.Init();
            m_SurvivorData.Init(roleInfo?.Survivors);
            m_DungeonData.Init(roleInfo?.Dungeons);
            m_ChaoZhiData.Init();
            m_PeakRankData.Init();
            m_tradeTruckData.Init(roleInfo?.Trade);
            m_UAVData.Init(roleInfo?.Uav);
            m_TeamData.Init(roleInfo?.Teams);
            PrivilegeData.Init(roleInfo?.Privileges);

            InitPushEvent();
        }

        protected virtual void Update()
        {
            m_UpdatePerSecond += Time.deltaTime;
            if (m_UpdatePerSecond >= 1)
            {
                UpdatePerSecond(1);
                m_UpdatePerSecond -= 1;
            }
        }

        /// <summary>
        /// 每秒刷新一次
        /// </summary>
        /// <param name="dt"></param>
        protected virtual void UpdatePerSecond(float dt)
        {
            m_QueueData.UpdatePerSecond(dt);
        }

        /// <summary>
        /// 初始化推送事件
        /// </summary>
        void InitPushEvent()
        {
            PushRoleArticles();
            PushPayment();
            PushTaskChange();
            PushPowerChange();
            PushArticle();
            PushTechChange();
        }
        /// <summary>
        /// 推送科技中心变化
        /// </summary>
        void PushTechChange()
        {
            NetEventDispatch.Instance.RegisterEvent((int)Protocol.MessageID.PushTechChange, (message) =>
            {
                ColorLog.Pink("推送科技中心变化", message);
                if (message != null)
                {
                    var data = (Tech.PushTechChange)message;
                    var tech = data.Tech;
                    TechData.OnTechChange(tech);
                }
            });
        }   

        /// <summary>
        /// 推送道具、英雄、装备变化
        /// </summary>
        void PushRoleArticles()
        {
            NetEventDispatch.Instance.RegisterEvent((int)Protocol.MessageID.PushRoleArticles, (message) =>
            {
                if (message != null)
                {
                    var articleChanges = (Article.ArticleChanges)message;
                    foreach (var data in articleChanges.Changes)
                    {
                        // 道具
                        if (data.Type == PbGameconfig.itemtype.Item)
                        {
                            var item = Item.ItemChanges.Descriptor.Parser.ParseFrom(data.Change) as Item.ItemChanges;
                            List<itemid> itemids = new List<itemid>();
                            foreach (var value in item.List)
                            {
                                ItemModule itemModule = new ItemModule();
                                itemModule.SetData((itemid)value.Code, value.Amount);
                                m_BagData.ChangeItem(itemModule);
                                itemids.Add((itemid)value.Code);
                                ColorLog.Pink("背包变化", "id = ", value.Code, "count = ", value.Amount);
                            }

                            GameEntry.Event.Fire(ItemChangeEventArgs.EventId, ItemChangeEventArgs.Create(itemids));
                        }
                        // 英雄
                        else if (data.Type == PbGameconfig.itemtype.Hero)
                        {
                            var hero = Hero.HeroChanges.Descriptor.Parser.ParseFrom(data.Change) as Hero.HeroChanges;
                            HeroData.OnHeroChanges(hero);
                        }
                        // 装备
                        else if (data.Type == PbGameconfig.itemtype.Heroequipment)
                        {
                            var heroEquipment = Equip.EquipChanges.Descriptor.Parser.ParseFrom(data.Change) as Equip.EquipChanges;
                            EquipmentData.OnEquipmentChanges(heroEquipment);
                        }
                        // 幸存者
                        else if (data.Type == PbGameconfig.itemtype.Survivor)
                        {
                            var roleSurvivor = RoleSurvivor.Descriptor.Parser.ParseFrom(data.Change) as RoleSurvivor;
                            SurvivorData.OnSurvivorChange(roleSurvivor);
                        }
                    }
                }
            });
        }

        /// <summary>
        /// 推送充值订单完成
        /// </summary>
        void PushPayment()
        {
            NetEventDispatch.Instance.RegisterEvent((int)Protocol.MessageID.PushPayment, (message) =>
            {
                ColorLog.Pink("充值订单完成", message);
                if (message is Payment.PushPaymentResp resp)
                {
                    ToolScriptExtend.DisplayRewardGet(resp.Rewards.ToList(), true);
                    GameEntry.Event.Fire(PaymentFinishEventArgs.EventId, PaymentFinishEventArgs.Create());
                }
            });
        }

        /// <summary>
        /// 推送任务变更
        /// </summary>
        void PushTaskChange()
        {
            NetEventDispatch.Instance.RegisterEvent((int)Protocol.MessageID.TaskChangePush, (message) =>
            {
                ColorLog.Pink("任务变更", message);
                if (message != null)
                {
                    var mes = (Task.TaskChangePush)message;
                    var changes = mes.Changes;
                    foreach (var item in changes)
                    {
                        Task.TaskChange change = item;
                        TaskData.ChangeTask(change);
                    }
                    GameEntry.Event.Fire(TaskChangeEventArgs.EventId, BagChangeEventArgs.Create());
                }
            });
        }

        /// <summary>
        /// 推送角色战力变化
        /// </summary>
        void PushPowerChange()
        {
            NetEventDispatch.Instance.RegisterEvent((int)Protocol.MessageID.PushPowerChange, (message) =>
            {
                ColorLog.Pink("推送角色战力变化", message);
                if (message != null)
                {
                    var data = (Role.PushPowerChange)message;
                    if (data.Curr > data.Prev)
                    {
                        GameEntry.RoleData.Power = data.Curr;

                        if (!GameEntry.UI.HasUIForm(EnumUIForm.UIFightChangeForm)) { GameEntry.UI.OpenUIForm(EnumUIForm.UIFightChangeForm, data); }
                        else { GameEntry.UI.RefreshUIForm(EnumUIForm.UIFightChangeForm, data); }
                    }
                }
            });
        }

        /// <summary>
        /// 推送物品（恭喜获得）
        /// </summary>
        void PushArticle()
        {
            NetEventDispatch.Instance.RegisterEvent((int)Protocol.MessageID.PushArticle, (message) =>
            {
                ColorLog.Pink("推送物品（恭喜获得）", message);
                if (message != null)
                {
                    var data = (Article.PushArticle)message;
                    var list = data.Articles;
                    List<reward> rewards = new();
                    foreach (var item in list)
                    {
                        rewards.Add(new reward()
                        {
                            item_id = (itemid)item.Code,
                            num = item.Amount
                        });
                    }

                    if (rewards.Count > 0)
                        GameEntry.UI.OpenUIForm(EnumUIForm.UIRewardGetForm, rewards);
                }
            });
        }
    }
}
