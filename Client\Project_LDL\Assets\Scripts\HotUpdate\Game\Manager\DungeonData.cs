using System;
using System.Collections.Generic;
using Dungeon;
using Game.Hotfix.Config;
using Roledata;
using UnityEngine;

namespace Game.Hotfix
{
    public class DungeonData
    {
        public int CurDungeonId => m_CurDungeonId;

        private int m_CurDungeonId;

        /// <summary>
        /// 挂机奖励开始计算时间戳
        /// </summary>
        public long AccumulatedRewardAt { get; set; }

        public DungeonData()
        {

        }

        public void Init(RoleDungeon dungeons)
        {
            if (dungeons != null)
            {
                m_CurDungeonId = dungeons.DungeonId;
                AccumulatedRewardAt = dungeons.AccumulatedRewardAt;
            }
        }

        public void SetDungeonId(int id, long rewardAt)
        {
            m_CurDungeonId = id;
            AccumulatedRewardAt = rewardAt;
        }

        public int GetAccumulatedTime()
        {
            var dic = GameEntry.LogicData.BuildingData.GetAttrbutesConfigByType(buildtype.buildtype_truck, 1);
            dic.TryGetValue(attributes_type.attributes_type_83, out var time);
            return (int)time;
        }

        public void DungeonLoad(Action<DungeonLoadResp> callback = null)
        {
            var req = new DungeonLoadReq();

            GameEntry.LDLNet.Send(Protocol.MessageID.DungeonLoad, req, (message) =>
            {
                var resp = (DungeonLoadResp)message;
                if (resp != null)
                {
                    m_CurDungeonId = resp.Id;
                    callback?.Invoke(resp);
                }
            });
        }


        /// <summary>
        /// 关卡领取宝箱奖励, 3404
        /// </summary>
        /// <param name="id"></param>
        /// <param name="callback"></param>
        public void DungeonClaimBox(int id, Action callback = null)
        {
            var req = new DungeonClaimBoxReq
            {
                Id = id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.DungeonClaimBox, req, (message) =>
            {
                var resp = (DungeonClaimBoxResp)message;
                if (resp != null)
                {
                    callback?.Invoke();
                }
            });
        }

        /// <summary>
        /// 关卡挂机奖励, 3405
        /// </summary>
        /// <param name="id"></param>
        /// <param name="callback"></param>
        public void DungeonAccumulatedRewards(Action callback = null)
        {
            var req = new DungeonAccumulatedRewardsReq();

            GameEntry.LDLNet.Send(Protocol.MessageID.DungeonAccumulatedRewards, req, (message) =>
            {
                var resp = (DungeonAccumulatedRewardsResp)message;
                if (resp != null)
                {
                    AccumulatedRewardAt = resp.AccumulatedRewardAt;
                    callback?.Invoke();
                }
            });
        }
    }
}
