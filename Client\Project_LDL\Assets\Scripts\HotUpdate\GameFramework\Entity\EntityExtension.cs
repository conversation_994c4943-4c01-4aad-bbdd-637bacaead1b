//------------------------------------------------------------
// Game Framework
// Copyright © 2013-2021 Jiang <PERSON>. All rights reserved.
// Homepage: https://gameframework.cn/
// Feedback: mailto:<EMAIL>
//------------------------------------------------------------

using GameFramework.DataTable;
using System;
using Game.Hotfix.Config;
using UnityEngine;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public static class EntityExtension
    {
        // 关于 EntityId 的约定：
        // 0 为无效
        // 正值用于和服务器通信的实体（如玩家角色、NPC、怪等，服务器只产生正值）
        // 负值用于本地生成的临时实体（如特效、FakeObject等）
        private static int s_SerialId = 0;
        
        public static int GenerateSerialId(this EntityComponent entityComponent)
        {
            return --s_SerialId;
        }
        
        public static Entity GetGameEntity(this EntityComponent entityComponent, int entityId)
        {
            UnityGameFramework.Runtime.Entity entity = entityComponent.GetEntity(entityId);
            if (entity == null)
            {
                return null;
            }
        
            return (Entity)entity.Logic;
        }

        public static void HideEntity(this EntityComponent entityComponent, Entity entity)
        {
            entityComponent.HideEntity(entity.Entity);
        }

        public static void AttachEntity(this EntityComponent entityComponent, Entity entity, int ownerId, string parentTransformPath = null, object userData = null)
        {
            entityComponent.AttachEntity(entity.Entity, ownerId, parentTransformPath, userData);
        }
        
        public static void ShowEntity(this EntityComponent entityComponent, Type logicType, string entityGroup, int priority, EntityData data)
        {
            if (data == null)
            {
                Log.Warning("Data is invalid.");
                return;
            }
        
            // if (GameEntry.LubanTable.TryGetTables(out Cfg.Tables tables))
            // {
            //     var tbEntity = tables.TbEntity.Get(data.TypeId);
            //     
            //     if (tbEntity == null)
            //     {
            //         Log.Warning("Can not load entity id '{0}' from data table.", data.TypeId.ToString());
            //         return;
            //     }
            //
            //     entityComponent.ShowEntity(data.Id, logicType, AssetUtility.GetEntityAsset(tbEntity.AssetName), entityGroup, priority, data);
            // }
        }

        public static void ShowBuilding(this EntityComponent entityComponent,ED_Building data)
        {

            BuildingModule buildingModule = data.buildingData;
            
            build_config config = Game.GameEntry.LDLTable.GetTableById<build_config>(buildingModule.BuildingId);
            if (config == null)
            {
                Log.Warning("Build config is not found."+buildingModule.BuildingId);
                return;
            }
            Type type = buildingModule.GetBuildingEntityLogicType();
            var path = "Assets/ResPackage/Prefab/Building/buildingContainer.prefab";
            entityComponent.ShowEntity(data.Id, type, path, "Default", 1, data);
        }

        public static int ShowBuildingDisplay(this EntityComponent entityComponent,string path,int parentId,Vector3? scale = null,Vector3? pos = null)
        {
            var data = new ED_BuildingDisplay(Game.GameEntry.Entity.GenerateSerialId(), parentId);
            if (scale != null)
                data.Scale = scale.Value;
            if (pos != null)
                data.Position = pos.Value;
            entityComponent.ShowEntity(data.Id, typeof(EL_BuildingDisplay), path, "Default", 1, data);
            return data.Id;
        }
        
        public static int ShowBuildingDisplaySoldier(this EntityComponent entityComponent,string path,int parentId,Vector3? scale = null,Vector3? pos = null)
        {
            var data = new ED_BuildingDisplay(Game.GameEntry.Entity.GenerateSerialId(), parentId);
            if (scale != null)
                data.Scale = scale.Value;
            if (pos != null)
                data.Position = pos.Value;
            entityComponent.ShowEntity(data.Id, typeof(EL_BuildingDisplaySoldier), path, "Default", 1, data);
            return data.Id;
        }      
        
        public static int ShowSoldierDisplay(this EntityComponent entityComponent,string path,int parentId,int soldierAreaIndex = 0,int moveTargetId = 0,Vector3? moveToPos = null,Vector3? pos = null)
        {
            var data = new ED_SoldierDisplay(Game.GameEntry.Entity.GenerateSerialId(), parentId,soldierAreaIndex,moveTargetId,moveToPos);
            if (pos != null)
                data.Position = pos.Value;
            entityComponent.ShowEntity(data.Id, typeof(EL_SoldierDisplay), path, "Default", 1, data);
            return data.Id;
        }
        
        public static int ShowCommonDisplay(this EntityComponent entityComponent,string path,int parentId,string parentPath = null,Vector3? pos = null,Quaternion? euler = null,Vector3? scale = null)
        {
            var data = new ED_CommonDisplay(Game.GameEntry.Entity.GenerateSerialId(), parentId,parentPath);
            if (pos != null)
                data.Position = pos.Value;
            if (euler != null)
                data.Rotation = euler.Value;
            if (scale != null)
                data.Scale = scale.Value;
            entityComponent.ShowEntity(data.Id, typeof(EL_CommonDisplay), path, "Default", 1, data);
            return data.Id;
        }
        
        public static int ShowBuildingPreview(this EntityComponent entityComponent,BuildingModule buildingData,BuidlingPreviewType previewType = BuidlingPreviewType.Build)
        {
            var data = new ED_BuidlingPreview(buildingData.UID,buildingData,null,previewType);
            entityComponent.ShowEntity(data.Uid, typeof(EL_BuidlingPreview), "Assets/ResPackage/Prefab/Building/buildingPreview.prefab", "Default", 1, data);
            return data.Uid;
        }
        
        public static int ShowBuildingPreview(this EntityComponent entityComponent,EL_Building building,BuidlingPreviewType previewType = BuidlingPreviewType.Move)
        {
            int uid = Game.GameEntry.Entity.GenerateSerialId();
            var data = new ED_BuidlingPreview(uid, null, building, previewType);
            entityComponent.ShowEntity(data.Uid, typeof(EL_BuidlingPreview), "Assets/ResPackage/Prefab/Building/buildingPreview.prefab", "Default", 1, data);
            return uid;
        }

        public static int ShowTownMovePreview(this EntityComponent entityComponent,string path,EntityData data,Type type)
        {
            entityComponent.ShowEntity(data.Id, type, path, "Default", 1, data);
            return data.Id;
        }

        public static int ShowWall(this EntityComponent entityComponent,MainCityWallModule wallModule)
        {
            var data = new ED_Wall(wallModule.Uid, wallModule);
            entityComponent.ShowEntity(wallModule.Uid, typeof(EL_Wall), wallModule.GetPrfabPath(), "Default", 1, data);
            return wallModule.Uid;
        }

        /// <summary>
        /// 显示 主城pve 路径
        /// </summary>
        /// <param name="entityComponent"></param>
        /// <param name="module"></param>
        /// <returns></returns>
        public static int ShowPvePathGround(this EntityComponent entityComponent,PvePathModule module)
        {
            int uid = Game.GameEntry.Entity.GenerateSerialId();
            module.SetUid(uid); 
            var path = "Assets/ResPackage/MainCity/Prefab/PvePathContainer.prefab";
            ED_PvePath edPvePath = new ED_PvePath(module);
            edPvePath.Position = module.GetPosition();
            entityComponent.ShowEntity(uid, typeof(EL_PvePath), path, "Default", 1, edPvePath);
            return uid;
        }

        /// <summary>
        /// 显示 主城pve 路径 地面
        /// </summary>
        /// <param name="entityComponent"></param>
        /// <param name="path"></param>
        /// <param name="parentId"></param>
        /// <param name="scale"></param>
        /// <param name="rotation"></param>
        /// <returns></returns>
        public static int ShowPvePathGroundDisplay(this EntityComponent entityComponent,string path,int parentId,string hideAnimation,Vector3? scale = null,Quaternion? rotation = null)
        {
            var data = new ED_PvePathGroundDisplay(Game.GameEntry.Entity.GenerateSerialId(), parentId, hideAnimation);
            if (scale != null)
                data.Scale = scale.Value;
            if (rotation != null)
                data.Rotation = rotation.Value;
            data.Position = new Vector3(1, 0.02f, 1);
            entityComponent.ShowEntity(data.Id, typeof(EL_PvePathGroundDisplay), path, "Default", 1, data);
            return data.Id;
        }
        
        /// <summary>
        /// 显示 主城pve 路径 角色(宝箱/怪物 等)
        /// </summary>
        /// <param name="entityComponent"></param>
        /// <param name="path"></param>
        /// <param name="parentId"></param>
        /// <param name="scale"></param>
        /// <param name="rotation"></param>
        /// <returns></returns>
        public static int ShowPvePathObjDisplay(this EntityComponent entityComponent,string path,int parentId,Vector3? scale = null,Quaternion? rotation = null)
        {
            var data = new ED_PvePathObjDisplay(Game.GameEntry.Entity.GenerateSerialId(), parentId);
            if (scale != null)
                data.Scale = scale.Value;
            if (rotation != null)
                data.Rotation = rotation.Value;
            data.Position = new Vector3(1, 0.02f, 1);
            entityComponent.ShowEntity(data.Id, typeof(EL_PvePathObjDisplay), path, "Default", 1, data);
            return data.Id;
        }

        public static int ShowPvePathAgent(this EntityComponent entityComponent,PvePathModule module)
        {
            int uid = Game.GameEntry.Entity.GenerateSerialId();
            var edPvePath = new ED_PvePathAgent(uid,module);
            edPvePath.Position = module.GetPosition();
            
            var path = "Assets/ResPackage/MainCity/Prefab/PvePathAgentContainer.prefab";
            
            entityComponent.ShowEntity(uid, typeof(EL_PvePathAgent), path, "Default", 1, edPvePath);
            return uid;
        }
        
        public static int ShowPvePathAgentDisplay(this EntityComponent entityComponent,string path,int parentId,Vector3? scale = null,Quaternion? rotation = null)
        {
            var data = new ED_PvePathAgentDisplay(Game.GameEntry.Entity.GenerateSerialId(), parentId);
            if (scale != null)
                data.Scale = scale.Value;
            if (rotation != null)
                data.Rotation = rotation.Value;
            data.Position = new Vector3(1, 0.02f, 1);
            entityComponent.ShowEntity(data.Id, typeof(EL_PvePathAgentDisplay), path, "Default", 1, data);
            return data.Id;
        }
        
        public static int ShowBattleUnit(this EntityComponent entityComponent,Type type,string path,Vector3 p,Quaternion r,Vector3 s,ED_BattleUnitBase data)
        {
            entityComponent.ShowEntity(data.Id, type, path, "Default", 1, data);
            return data.Id;
        }

        
        public static int ShowMainCityAreaDecoration(this EntityComponent entityComponent,MainCityAreaDecorationModule decoration)
        {
            int uid = Game.GameEntry.Entity.GenerateSerialId();
            decoration.SetUid(uid);
            
            ED_AreaDecoration data = new ED_AreaDecoration(uid);
            data.Position = decoration.GetPosition();
            data.Module = decoration;
            
            entityComponent.ShowEntity(uid, typeof(EL_AreaDecoration), decoration.GetPrefab(), "Default", 1, data);
            return uid;
        }

        public static int ShowMainCityAreaUnlock(this EntityComponent entityComponent,MainCityAreaModule module)
        {
            var uid = module.Uid;
            ED_AreaUnlock data = new ED_AreaUnlock(uid);
            data.Module = module;
            data.Position = module.GetPos();
            
            entityComponent.ShowEntity(uid, typeof(EL_AreaUnlock), "Assets/ResPackage/Prefab/MainCity/areaUnlock.prefab", "Default", 1, data);
            return uid;
        }
        
        public static int ShowCommonEffect(this EntityComponent entityComponent,int effectId,Vector3 p,Quaternion? r = null,Vector3? s = null)
        {
            ED_CommonEffect data = new ED_CommonEffect(Game.GameEntry.Entity.GenerateSerialId(), effectId);
            data.Position = p;
            if (r != null)
                data.Rotation = r.Value;
            if (s != null)
                data.Scale = s.Value;
            entityComponent.ShowEntity(data.Id, typeof(EL_CommonEffect), data.GetPath(), "Default", 1, data);
            return data.Id;
        }
        
        public static int ShowWorldMapDisplay(this EntityComponent entityComponent,string path,Vector3 p,Quaternion? r = null,Vector3? s = null)
        {
            ED_WorldMapDisplay data = new ED_WorldMapDisplay(Game.GameEntry.Entity.GenerateSerialId());
            data.Position = p;
            if (r != null)
                data.Rotation = r.Value;
            if (s != null)
                data.Scale = s.Value;
            entityComponent.ShowEntity(data.Id, typeof(EL_WorldMapDisplay), path, "Default", 1, data);
            return data.Id;
        }

        public static int ShowWorldMapDisplay(this EntityComponent entityComponent,string path,EntityData data,Type type)
        {
            entityComponent.ShowEntity(data.Id, type, path, "Default", 1, data);
            return data.Id;
        }
        
        public static int ShowTrain(this EntityComponent entityComponent)
        {
            ED_Train data = new(Game.GameEntry.Entity.GenerateSerialId());
            var path = "Assets/ResPackage/MainCity/Prefab/train_normal.prefab";
            entityComponent.ShowEntity(data.Id, typeof(EL_Train), path, "Default", 1, data);
            return data.Id;
        }
        
        // public static int ShowBattleEffect(this EntityComponent entityComponent,string path,Vector3 p,Quaternion r,Vector3 s)
        // {
        //     ED_BattleEffect data = new ED_BattleEffect(Game.GameEntry.Entity.GenerateSerialId(),p, r, s);
        //     entityComponent.ShowBattleUnit(typeof(EL_BattleEffect), path, p, r, s, data);
        //     return data.Uid;
        // }
        //
        // public static int ShowBattleHero(this EntityComponent entityComponent,string path,Vector3 p,Quaternion r,Vector3 s)
        // {
        //     ED_BattleHero data = new ED_BattleHero(Game.GameEntry.Entity.GenerateSerialId(), p, r, s);
        //     entityComponent.ShowBattleUnit(typeof(EL_BattleHero), path, p, r, s, data);
        //     return data.Uid;
        // }
        
        
    }
}
