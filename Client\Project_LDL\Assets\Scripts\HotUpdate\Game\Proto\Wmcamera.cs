// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: wmcamera.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Wmcamera {

  /// <summary>Holder for reflection information generated from wmcamera.proto</summary>
  public static partial class WmcameraReflection {

    #region Descriptor
    /// <summary>File descriptor for wmcamera.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static WmcameraReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Cg53bWNhbWVyYS5wcm90bxIId21jYW1lcmEaCnRvd24ucHJvdG8aCm1pbmUu",
            "cHJvdG8aC3Ryb29wLnByb3RvIjEKDUNhbWVyYUluaXRSZXESEAoIc2VydmVy",
            "SUQYASABKA0SDgoGZ2F0ZUlEGAIgASgNIjcKDkNhbWVyYUluaXRSZXNwEhEK",
            "CWdyaWRXaWR0aBgBIAEoDRISCgpncmlkSGVpZ2h0GAIgASgNIhEKD0NhbWVy",
            "YVJlbW92ZVJlcSISChBDYW1lcmFSZW1vdmVSZXNwImoKDUNhbWVyYU1vdmVS",
            "ZXESCQoBeBgBIAEoDRIJCgF5GAIgASgNEiQKBWxheWVyGAMgASgOMhUud21j",
            "YW1lcmEuQ2FtZXJhTGF5ZXISDQoFd2lkdGgYBCABKA0SDgoGaGVpZ2h0GAUg",
            "ASgNIpoBCg5DYW1lcmFNb3ZlUmVzcBITCgtkZWxldGVHcmlkcxgBIAMoDRIZ",
            "CgV0b3ducxgCIAMoCzIKLnRvd24uVG93bhIQCghtb25zdGVycxgDIAMoBBIN",
            "CgVmb3J0cxgEIAMoBBIZCgVtaW5lcxgFIAMoCzIKLm1pbmUuTWluZRIcCgZ0",
            "cm9vcHMYBiADKAsyDC50cm9vcC5Ucm9vcCIoCgxEZWxldGVFbnRpdHkSCgoC",
            "aWQYASABKAQSDAoEdHlwZRgCIAEoDSKsAQoQQ2FtZXJhVXBkYXRlUHVzaBIj",
            "CgNpZHMYASADKAsyFi53bWNhbWVyYS5EZWxldGVFbnRpdHkSGQoFdG93bnMY",
            "AiADKAsyCi50b3duLlRvd24SEAoIbW9uc3RlcnMYAyADKAQSDQoFZm9ydHMY",
            "BCADKAQSGQoFbWluZXMYBSADKAsyCi5taW5lLk1pbmUSHAoGdHJvb3BzGAYg",
            "AygLMgwudHJvb3AuVHJvb3AqSQoLQ2FtZXJhTGF5ZXISFAoQQ2FtZXJhTGF5",
            "ZXJfTm9uZRAAEhEKDUNhbWVyYUxheWVyXzEQARIRCg1DYW1lcmFMYXllcl8y",
            "EAJCG1oZc2VydmVyL2FwaS9wYi9wYl93bWNhbWVyYWIGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Town.TownReflection.Descriptor, global::Mine.MineReflection.Descriptor, global::Troop.TroopReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Wmcamera.CameraLayer), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Wmcamera.CameraInitReq), global::Wmcamera.CameraInitReq.Parser, new[]{ "ServerID", "GateID" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Wmcamera.CameraInitResp), global::Wmcamera.CameraInitResp.Parser, new[]{ "GridWidth", "GridHeight" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Wmcamera.CameraRemoveReq), global::Wmcamera.CameraRemoveReq.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Wmcamera.CameraRemoveResp), global::Wmcamera.CameraRemoveResp.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Wmcamera.CameraMoveReq), global::Wmcamera.CameraMoveReq.Parser, new[]{ "X", "Y", "Layer", "Width", "Height" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Wmcamera.CameraMoveResp), global::Wmcamera.CameraMoveResp.Parser, new[]{ "DeleteGrids", "Towns", "Monsters", "Forts", "Mines", "Troops" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Wmcamera.DeleteEntity), global::Wmcamera.DeleteEntity.Parser, new[]{ "Id", "Type" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Wmcamera.CameraUpdatePush), global::Wmcamera.CameraUpdatePush.Parser, new[]{ "Ids", "Towns", "Monsters", "Forts", "Mines", "Troops" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  public enum CameraLayer {
    /// <summary>
    /// 占位
    /// </summary>
    [pbr::OriginalName("CameraLayer_None")] None = 0,
    /// <summary>
    /// 第一层(所有地图实体)
    /// </summary>
    [pbr::OriginalName("CameraLayer_1")] _1 = 1,
    /// <summary>
    /// 第二层(主城与移动部队)
    /// </summary>
    [pbr::OriginalName("CameraLayer_2")] _2 = 2,
  }

  #endregion

  #region Messages
  /// <summary>
  /// 初始化镜头
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CameraInitReq : pb::IMessage<CameraInitReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CameraInitReq> _parser = new pb::MessageParser<CameraInitReq>(() => new CameraInitReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CameraInitReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Wmcamera.WmcameraReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CameraInitReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CameraInitReq(CameraInitReq other) : this() {
      serverID_ = other.serverID_;
      gateID_ = other.gateID_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CameraInitReq Clone() {
      return new CameraInitReq(this);
    }

    /// <summary>Field number for the "serverID" field.</summary>
    public const int ServerIDFieldNumber = 1;
    private uint serverID_;
    /// <summary>
    /// 镜头需要查看的服务器ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint ServerID {
      get { return serverID_; }
      set {
        serverID_ = value;
      }
    }

    /// <summary>Field number for the "gateID" field.</summary>
    public const int GateIDFieldNumber = 2;
    private uint gateID_;
    /// <summary>
    /// 不用传值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint GateID {
      get { return gateID_; }
      set {
        gateID_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CameraInitReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CameraInitReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ServerID != other.ServerID) return false;
      if (GateID != other.GateID) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ServerID != 0) hash ^= ServerID.GetHashCode();
      if (GateID != 0) hash ^= GateID.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ServerID != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(ServerID);
      }
      if (GateID != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(GateID);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ServerID != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(ServerID);
      }
      if (GateID != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(GateID);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ServerID != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(ServerID);
      }
      if (GateID != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(GateID);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CameraInitReq other) {
      if (other == null) {
        return;
      }
      if (other.ServerID != 0) {
        ServerID = other.ServerID;
      }
      if (other.GateID != 0) {
        GateID = other.GateID;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            ServerID = input.ReadUInt32();
            break;
          }
          case 16: {
            GateID = input.ReadUInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            ServerID = input.ReadUInt32();
            break;
          }
          case 16: {
            GateID = input.ReadUInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CameraInitResp : pb::IMessage<CameraInitResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CameraInitResp> _parser = new pb::MessageParser<CameraInitResp>(() => new CameraInitResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CameraInitResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Wmcamera.WmcameraReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CameraInitResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CameraInitResp(CameraInitResp other) : this() {
      gridWidth_ = other.gridWidth_;
      gridHeight_ = other.gridHeight_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CameraInitResp Clone() {
      return new CameraInitResp(this);
    }

    /// <summary>Field number for the "gridWidth" field.</summary>
    public const int GridWidthFieldNumber = 1;
    private uint gridWidth_;
    /// <summary>
    /// 格子div宽度
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint GridWidth {
      get { return gridWidth_; }
      set {
        gridWidth_ = value;
      }
    }

    /// <summary>Field number for the "gridHeight" field.</summary>
    public const int GridHeightFieldNumber = 2;
    private uint gridHeight_;
    /// <summary>
    /// 
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint GridHeight {
      get { return gridHeight_; }
      set {
        gridHeight_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CameraInitResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CameraInitResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (GridWidth != other.GridWidth) return false;
      if (GridHeight != other.GridHeight) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (GridWidth != 0) hash ^= GridWidth.GetHashCode();
      if (GridHeight != 0) hash ^= GridHeight.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (GridWidth != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(GridWidth);
      }
      if (GridHeight != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(GridHeight);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (GridWidth != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(GridWidth);
      }
      if (GridHeight != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(GridHeight);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (GridWidth != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(GridWidth);
      }
      if (GridHeight != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(GridHeight);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CameraInitResp other) {
      if (other == null) {
        return;
      }
      if (other.GridWidth != 0) {
        GridWidth = other.GridWidth;
      }
      if (other.GridHeight != 0) {
        GridHeight = other.GridHeight;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            GridWidth = input.ReadUInt32();
            break;
          }
          case 16: {
            GridHeight = input.ReadUInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            GridWidth = input.ReadUInt32();
            break;
          }
          case 16: {
            GridHeight = input.ReadUInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 移除镜头
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CameraRemoveReq : pb::IMessage<CameraRemoveReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CameraRemoveReq> _parser = new pb::MessageParser<CameraRemoveReq>(() => new CameraRemoveReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CameraRemoveReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Wmcamera.WmcameraReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CameraRemoveReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CameraRemoveReq(CameraRemoveReq other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CameraRemoveReq Clone() {
      return new CameraRemoveReq(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CameraRemoveReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CameraRemoveReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CameraRemoveReq other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CameraRemoveResp : pb::IMessage<CameraRemoveResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CameraRemoveResp> _parser = new pb::MessageParser<CameraRemoveResp>(() => new CameraRemoveResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CameraRemoveResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Wmcamera.WmcameraReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CameraRemoveResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CameraRemoveResp(CameraRemoveResp other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CameraRemoveResp Clone() {
      return new CameraRemoveResp(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CameraRemoveResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CameraRemoveResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CameraRemoveResp other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 移动镜头
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CameraMoveReq : pb::IMessage<CameraMoveReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CameraMoveReq> _parser = new pb::MessageParser<CameraMoveReq>(() => new CameraMoveReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CameraMoveReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Wmcamera.WmcameraReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CameraMoveReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CameraMoveReq(CameraMoveReq other) : this() {
      x_ = other.x_;
      y_ = other.y_;
      layer_ = other.layer_;
      width_ = other.width_;
      height_ = other.height_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CameraMoveReq Clone() {
      return new CameraMoveReq(this);
    }

    /// <summary>Field number for the "x" field.</summary>
    public const int XFieldNumber = 1;
    private uint x_;
    /// <summary>
    /// x坐标
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint X {
      get { return x_; }
      set {
        x_ = value;
      }
    }

    /// <summary>Field number for the "y" field.</summary>
    public const int YFieldNumber = 2;
    private uint y_;
    /// <summary>
    /// y坐标
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Y {
      get { return y_; }
      set {
        y_ = value;
      }
    }

    /// <summary>Field number for the "layer" field.</summary>
    public const int LayerFieldNumber = 3;
    private global::Wmcamera.CameraLayer layer_ = global::Wmcamera.CameraLayer.None;
    /// <summary>
    /// 层级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Wmcamera.CameraLayer Layer {
      get { return layer_; }
      set {
        layer_ = value;
      }
    }

    /// <summary>Field number for the "width" field.</summary>
    public const int WidthFieldNumber = 4;
    private uint width_;
    /// <summary>
    /// 宽度
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Width {
      get { return width_; }
      set {
        width_ = value;
      }
    }

    /// <summary>Field number for the "height" field.</summary>
    public const int HeightFieldNumber = 5;
    private uint height_;
    /// <summary>
    /// 高度
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Height {
      get { return height_; }
      set {
        height_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CameraMoveReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CameraMoveReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (X != other.X) return false;
      if (Y != other.Y) return false;
      if (Layer != other.Layer) return false;
      if (Width != other.Width) return false;
      if (Height != other.Height) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (X != 0) hash ^= X.GetHashCode();
      if (Y != 0) hash ^= Y.GetHashCode();
      if (Layer != global::Wmcamera.CameraLayer.None) hash ^= Layer.GetHashCode();
      if (Width != 0) hash ^= Width.GetHashCode();
      if (Height != 0) hash ^= Height.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (X != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(X);
      }
      if (Y != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(Y);
      }
      if (Layer != global::Wmcamera.CameraLayer.None) {
        output.WriteRawTag(24);
        output.WriteEnum((int) Layer);
      }
      if (Width != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(Width);
      }
      if (Height != 0) {
        output.WriteRawTag(40);
        output.WriteUInt32(Height);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (X != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(X);
      }
      if (Y != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(Y);
      }
      if (Layer != global::Wmcamera.CameraLayer.None) {
        output.WriteRawTag(24);
        output.WriteEnum((int) Layer);
      }
      if (Width != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(Width);
      }
      if (Height != 0) {
        output.WriteRawTag(40);
        output.WriteUInt32(Height);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (X != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(X);
      }
      if (Y != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Y);
      }
      if (Layer != global::Wmcamera.CameraLayer.None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Layer);
      }
      if (Width != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Width);
      }
      if (Height != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Height);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CameraMoveReq other) {
      if (other == null) {
        return;
      }
      if (other.X != 0) {
        X = other.X;
      }
      if (other.Y != 0) {
        Y = other.Y;
      }
      if (other.Layer != global::Wmcamera.CameraLayer.None) {
        Layer = other.Layer;
      }
      if (other.Width != 0) {
        Width = other.Width;
      }
      if (other.Height != 0) {
        Height = other.Height;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            X = input.ReadUInt32();
            break;
          }
          case 16: {
            Y = input.ReadUInt32();
            break;
          }
          case 24: {
            Layer = (global::Wmcamera.CameraLayer) input.ReadEnum();
            break;
          }
          case 32: {
            Width = input.ReadUInt32();
            break;
          }
          case 40: {
            Height = input.ReadUInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            X = input.ReadUInt32();
            break;
          }
          case 16: {
            Y = input.ReadUInt32();
            break;
          }
          case 24: {
            Layer = (global::Wmcamera.CameraLayer) input.ReadEnum();
            break;
          }
          case 32: {
            Width = input.ReadUInt32();
            break;
          }
          case 40: {
            Height = input.ReadUInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CameraMoveResp : pb::IMessage<CameraMoveResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CameraMoveResp> _parser = new pb::MessageParser<CameraMoveResp>(() => new CameraMoveResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CameraMoveResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Wmcamera.WmcameraReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CameraMoveResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CameraMoveResp(CameraMoveResp other) : this() {
      deleteGrids_ = other.deleteGrids_.Clone();
      towns_ = other.towns_.Clone();
      monsters_ = other.monsters_.Clone();
      forts_ = other.forts_.Clone();
      mines_ = other.mines_.Clone();
      troops_ = other.troops_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CameraMoveResp Clone() {
      return new CameraMoveResp(this);
    }

    /// <summary>Field number for the "deleteGrids" field.</summary>
    public const int DeleteGridsFieldNumber = 1;
    private static readonly pb::FieldCodec<uint> _repeated_deleteGrids_codec
        = pb::FieldCodec.ForUInt32(10);
    private readonly pbc::RepeatedField<uint> deleteGrids_ = new pbc::RepeatedField<uint>();
    /// <summary>
    /// 删除的格子ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<uint> DeleteGrids {
      get { return deleteGrids_; }
    }

    /// <summary>Field number for the "towns" field.</summary>
    public const int TownsFieldNumber = 2;
    private static readonly pb::FieldCodec<global::Town.Town> _repeated_towns_codec
        = pb::FieldCodec.ForMessage(18, global::Town.Town.Parser);
    private readonly pbc::RepeatedField<global::Town.Town> towns_ = new pbc::RepeatedField<global::Town.Town>();
    /// <summary>
    /// 新增主城
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Town.Town> Towns {
      get { return towns_; }
    }

    /// <summary>Field number for the "monsters" field.</summary>
    public const int MonstersFieldNumber = 3;
    private static readonly pb::FieldCodec<ulong> _repeated_monsters_codec
        = pb::FieldCodec.ForUInt64(26);
    private readonly pbc::RepeatedField<ulong> monsters_ = new pbc::RepeatedField<ulong>();
    /// <summary>
    /// 新增怪物
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<ulong> Monsters {
      get { return monsters_; }
    }

    /// <summary>Field number for the "forts" field.</summary>
    public const int FortsFieldNumber = 4;
    private static readonly pb::FieldCodec<ulong> _repeated_forts_codec
        = pb::FieldCodec.ForUInt64(34);
    private readonly pbc::RepeatedField<ulong> forts_ = new pbc::RepeatedField<ulong>();
    /// <summary>
    /// 新增集结怪物
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<ulong> Forts {
      get { return forts_; }
    }

    /// <summary>Field number for the "mines" field.</summary>
    public const int MinesFieldNumber = 5;
    private static readonly pb::FieldCodec<global::Mine.Mine> _repeated_mines_codec
        = pb::FieldCodec.ForMessage(42, global::Mine.Mine.Parser);
    private readonly pbc::RepeatedField<global::Mine.Mine> mines_ = new pbc::RepeatedField<global::Mine.Mine>();
    /// <summary>
    /// 新增矿
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Mine.Mine> Mines {
      get { return mines_; }
    }

    /// <summary>Field number for the "troops" field.</summary>
    public const int TroopsFieldNumber = 6;
    private static readonly pb::FieldCodec<global::Troop.Troop> _repeated_troops_codec
        = pb::FieldCodec.ForMessage(50, global::Troop.Troop.Parser);
    private readonly pbc::RepeatedField<global::Troop.Troop> troops_ = new pbc::RepeatedField<global::Troop.Troop>();
    /// <summary>
    /// 新增部队
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Troop.Troop> Troops {
      get { return troops_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CameraMoveResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CameraMoveResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!deleteGrids_.Equals(other.deleteGrids_)) return false;
      if(!towns_.Equals(other.towns_)) return false;
      if(!monsters_.Equals(other.monsters_)) return false;
      if(!forts_.Equals(other.forts_)) return false;
      if(!mines_.Equals(other.mines_)) return false;
      if(!troops_.Equals(other.troops_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= deleteGrids_.GetHashCode();
      hash ^= towns_.GetHashCode();
      hash ^= monsters_.GetHashCode();
      hash ^= forts_.GetHashCode();
      hash ^= mines_.GetHashCode();
      hash ^= troops_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      deleteGrids_.WriteTo(output, _repeated_deleteGrids_codec);
      towns_.WriteTo(output, _repeated_towns_codec);
      monsters_.WriteTo(output, _repeated_monsters_codec);
      forts_.WriteTo(output, _repeated_forts_codec);
      mines_.WriteTo(output, _repeated_mines_codec);
      troops_.WriteTo(output, _repeated_troops_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      deleteGrids_.WriteTo(ref output, _repeated_deleteGrids_codec);
      towns_.WriteTo(ref output, _repeated_towns_codec);
      monsters_.WriteTo(ref output, _repeated_monsters_codec);
      forts_.WriteTo(ref output, _repeated_forts_codec);
      mines_.WriteTo(ref output, _repeated_mines_codec);
      troops_.WriteTo(ref output, _repeated_troops_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += deleteGrids_.CalculateSize(_repeated_deleteGrids_codec);
      size += towns_.CalculateSize(_repeated_towns_codec);
      size += monsters_.CalculateSize(_repeated_monsters_codec);
      size += forts_.CalculateSize(_repeated_forts_codec);
      size += mines_.CalculateSize(_repeated_mines_codec);
      size += troops_.CalculateSize(_repeated_troops_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CameraMoveResp other) {
      if (other == null) {
        return;
      }
      deleteGrids_.Add(other.deleteGrids_);
      towns_.Add(other.towns_);
      monsters_.Add(other.monsters_);
      forts_.Add(other.forts_);
      mines_.Add(other.mines_);
      troops_.Add(other.troops_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10:
          case 8: {
            deleteGrids_.AddEntriesFrom(input, _repeated_deleteGrids_codec);
            break;
          }
          case 18: {
            towns_.AddEntriesFrom(input, _repeated_towns_codec);
            break;
          }
          case 26:
          case 24: {
            monsters_.AddEntriesFrom(input, _repeated_monsters_codec);
            break;
          }
          case 34:
          case 32: {
            forts_.AddEntriesFrom(input, _repeated_forts_codec);
            break;
          }
          case 42: {
            mines_.AddEntriesFrom(input, _repeated_mines_codec);
            break;
          }
          case 50: {
            troops_.AddEntriesFrom(input, _repeated_troops_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10:
          case 8: {
            deleteGrids_.AddEntriesFrom(ref input, _repeated_deleteGrids_codec);
            break;
          }
          case 18: {
            towns_.AddEntriesFrom(ref input, _repeated_towns_codec);
            break;
          }
          case 26:
          case 24: {
            monsters_.AddEntriesFrom(ref input, _repeated_monsters_codec);
            break;
          }
          case 34:
          case 32: {
            forts_.AddEntriesFrom(ref input, _repeated_forts_codec);
            break;
          }
          case 42: {
            mines_.AddEntriesFrom(ref input, _repeated_mines_codec);
            break;
          }
          case 50: {
            troops_.AddEntriesFrom(ref input, _repeated_troops_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class DeleteEntity : pb::IMessage<DeleteEntity>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<DeleteEntity> _parser = new pb::MessageParser<DeleteEntity>(() => new DeleteEntity());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<DeleteEntity> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Wmcamera.WmcameraReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DeleteEntity() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DeleteEntity(DeleteEntity other) : this() {
      id_ = other.id_;
      type_ = other.type_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DeleteEntity Clone() {
      return new DeleteEntity(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private ulong id_;
    /// <summary>
    /// 实体id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "type" field.</summary>
    public const int TypeFieldNumber = 2;
    private uint type_;
    /// <summary>
    /// 实体类型 1:主城 2:怪物 3:集结怪物 4:矿 5:部队
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Type {
      get { return type_; }
      set {
        type_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as DeleteEntity);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(DeleteEntity other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Type != other.Type) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0UL) hash ^= Id.GetHashCode();
      if (Type != 0) hash ^= Type.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(Id);
      }
      if (Type != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(Type);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(Id);
      }
      if (Type != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(Type);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(Id);
      }
      if (Type != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Type);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(DeleteEntity other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0UL) {
        Id = other.Id;
      }
      if (other.Type != 0) {
        Type = other.Type;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadUInt64();
            break;
          }
          case 16: {
            Type = input.ReadUInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadUInt64();
            break;
          }
          case 16: {
            Type = input.ReadUInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CameraUpdatePush : pb::IMessage<CameraUpdatePush>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CameraUpdatePush> _parser = new pb::MessageParser<CameraUpdatePush>(() => new CameraUpdatePush());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CameraUpdatePush> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Wmcamera.WmcameraReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CameraUpdatePush() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CameraUpdatePush(CameraUpdatePush other) : this() {
      ids_ = other.ids_.Clone();
      towns_ = other.towns_.Clone();
      monsters_ = other.monsters_.Clone();
      forts_ = other.forts_.Clone();
      mines_ = other.mines_.Clone();
      troops_ = other.troops_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CameraUpdatePush Clone() {
      return new CameraUpdatePush(this);
    }

    /// <summary>Field number for the "ids" field.</summary>
    public const int IdsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Wmcamera.DeleteEntity> _repeated_ids_codec
        = pb::FieldCodec.ForMessage(10, global::Wmcamera.DeleteEntity.Parser);
    private readonly pbc::RepeatedField<global::Wmcamera.DeleteEntity> ids_ = new pbc::RepeatedField<global::Wmcamera.DeleteEntity>();
    /// <summary>
    /// 删除id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Wmcamera.DeleteEntity> Ids {
      get { return ids_; }
    }

    /// <summary>Field number for the "towns" field.</summary>
    public const int TownsFieldNumber = 2;
    private static readonly pb::FieldCodec<global::Town.Town> _repeated_towns_codec
        = pb::FieldCodec.ForMessage(18, global::Town.Town.Parser);
    private readonly pbc::RepeatedField<global::Town.Town> towns_ = new pbc::RepeatedField<global::Town.Town>();
    /// <summary>
    /// 新增主城
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Town.Town> Towns {
      get { return towns_; }
    }

    /// <summary>Field number for the "monsters" field.</summary>
    public const int MonstersFieldNumber = 3;
    private static readonly pb::FieldCodec<ulong> _repeated_monsters_codec
        = pb::FieldCodec.ForUInt64(26);
    private readonly pbc::RepeatedField<ulong> monsters_ = new pbc::RepeatedField<ulong>();
    /// <summary>
    /// 新增怪物
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<ulong> Monsters {
      get { return monsters_; }
    }

    /// <summary>Field number for the "forts" field.</summary>
    public const int FortsFieldNumber = 4;
    private static readonly pb::FieldCodec<ulong> _repeated_forts_codec
        = pb::FieldCodec.ForUInt64(34);
    private readonly pbc::RepeatedField<ulong> forts_ = new pbc::RepeatedField<ulong>();
    /// <summary>
    /// 新增集结怪物
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<ulong> Forts {
      get { return forts_; }
    }

    /// <summary>Field number for the "mines" field.</summary>
    public const int MinesFieldNumber = 5;
    private static readonly pb::FieldCodec<global::Mine.Mine> _repeated_mines_codec
        = pb::FieldCodec.ForMessage(42, global::Mine.Mine.Parser);
    private readonly pbc::RepeatedField<global::Mine.Mine> mines_ = new pbc::RepeatedField<global::Mine.Mine>();
    /// <summary>
    /// 新增矿
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Mine.Mine> Mines {
      get { return mines_; }
    }

    /// <summary>Field number for the "troops" field.</summary>
    public const int TroopsFieldNumber = 6;
    private static readonly pb::FieldCodec<global::Troop.Troop> _repeated_troops_codec
        = pb::FieldCodec.ForMessage(50, global::Troop.Troop.Parser);
    private readonly pbc::RepeatedField<global::Troop.Troop> troops_ = new pbc::RepeatedField<global::Troop.Troop>();
    /// <summary>
    /// 新增部队
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Troop.Troop> Troops {
      get { return troops_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CameraUpdatePush);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CameraUpdatePush other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!ids_.Equals(other.ids_)) return false;
      if(!towns_.Equals(other.towns_)) return false;
      if(!monsters_.Equals(other.monsters_)) return false;
      if(!forts_.Equals(other.forts_)) return false;
      if(!mines_.Equals(other.mines_)) return false;
      if(!troops_.Equals(other.troops_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= ids_.GetHashCode();
      hash ^= towns_.GetHashCode();
      hash ^= monsters_.GetHashCode();
      hash ^= forts_.GetHashCode();
      hash ^= mines_.GetHashCode();
      hash ^= troops_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      ids_.WriteTo(output, _repeated_ids_codec);
      towns_.WriteTo(output, _repeated_towns_codec);
      monsters_.WriteTo(output, _repeated_monsters_codec);
      forts_.WriteTo(output, _repeated_forts_codec);
      mines_.WriteTo(output, _repeated_mines_codec);
      troops_.WriteTo(output, _repeated_troops_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      ids_.WriteTo(ref output, _repeated_ids_codec);
      towns_.WriteTo(ref output, _repeated_towns_codec);
      monsters_.WriteTo(ref output, _repeated_monsters_codec);
      forts_.WriteTo(ref output, _repeated_forts_codec);
      mines_.WriteTo(ref output, _repeated_mines_codec);
      troops_.WriteTo(ref output, _repeated_troops_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += ids_.CalculateSize(_repeated_ids_codec);
      size += towns_.CalculateSize(_repeated_towns_codec);
      size += monsters_.CalculateSize(_repeated_monsters_codec);
      size += forts_.CalculateSize(_repeated_forts_codec);
      size += mines_.CalculateSize(_repeated_mines_codec);
      size += troops_.CalculateSize(_repeated_troops_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CameraUpdatePush other) {
      if (other == null) {
        return;
      }
      ids_.Add(other.ids_);
      towns_.Add(other.towns_);
      monsters_.Add(other.monsters_);
      forts_.Add(other.forts_);
      mines_.Add(other.mines_);
      troops_.Add(other.troops_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            ids_.AddEntriesFrom(input, _repeated_ids_codec);
            break;
          }
          case 18: {
            towns_.AddEntriesFrom(input, _repeated_towns_codec);
            break;
          }
          case 26:
          case 24: {
            monsters_.AddEntriesFrom(input, _repeated_monsters_codec);
            break;
          }
          case 34:
          case 32: {
            forts_.AddEntriesFrom(input, _repeated_forts_codec);
            break;
          }
          case 42: {
            mines_.AddEntriesFrom(input, _repeated_mines_codec);
            break;
          }
          case 50: {
            troops_.AddEntriesFrom(input, _repeated_troops_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            ids_.AddEntriesFrom(ref input, _repeated_ids_codec);
            break;
          }
          case 18: {
            towns_.AddEntriesFrom(ref input, _repeated_towns_codec);
            break;
          }
          case 26:
          case 24: {
            monsters_.AddEntriesFrom(ref input, _repeated_monsters_codec);
            break;
          }
          case 34:
          case 32: {
            forts_.AddEntriesFrom(ref input, _repeated_forts_codec);
            break;
          }
          case 42: {
            mines_.AddEntriesFrom(ref input, _repeated_mines_codec);
            break;
          }
          case 50: {
            troops_.AddEntriesFrom(ref input, _repeated_troops_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
