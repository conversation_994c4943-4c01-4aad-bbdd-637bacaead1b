using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UITradeTrainBattlePlanForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnExchange1;
        [SerializeField] private UIButton m_btnExchange2;
        [SerializeField] private UIButton m_btnBack;
        [SerializeField] private UIButton m_btnFight;

        [SerializeField] private Transform m_transHeroItem;
        [SerializeField] private Transform m_transPlayerLeft;
        [SerializeField] private Transform m_transPlayerRight;
        [SerializeField] private Transform m_transTeamLeft;
        [SerializeField] private Transform m_transTeamRight;

        void InitBind()
        {
            m_btnExchange1.onClick.AddListener(OnBtnExchange1Click);
            m_btnExchange2.onClick.AddListener(OnBtnExchange2Click);
            m_btnBack.onClick.AddListener(OnBtnBackClick);
            m_btnFight.onClick.AddListener(OnBtnFightClick);
        }
    }
}
