using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Fight;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIBuildingWallForm : UGuiFormEx
    {
        private BuildingModule buildingModule;
        private List<DefendTeam> teamList = new();
        private List<Team.TeamType> teamTypeList = new();
        private List<Team.TeamType> extraList = new();

        private float deltaTime = 0;
        private int defendTime;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            m_TableViewV.GetItemCount = () => teamList.Count;
            m_TableViewV.GetItemGo = () => m_goItem;
            m_TableViewV.UpdateItemCell = OnUpdateItem;
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            buildingModule = userData as BuildingModule;
            OnUpdateInfo();
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        protected override void OnUpdate(float elapseSeconds, float realElapseSeconds)
        {
            base.OnUpdate(elapseSeconds, realElapseSeconds);

            deltaTime += elapseSeconds;
            if (deltaTime >= 1)
            {
                deltaTime = 0;

                if (defendTime > 0)
                {
                    defendTime--;
                    m_txtTime.text = defendTime > 0 ? TimeHelper.ToDateTimeText(defendTime) : "";
                }
            }
        }

        private void OnUpdateInfo()
        {
            var levelCfg = GameEntry.LogicData.BuildingData.GetBuildingLevelCfg(buildingModule.buildingCfg.build_type, buildingModule.LEVEL);
            var list = levelCfg.build_level_attributes;
            Dictionary<attributes_type, attributes> attrDic = new();
            for (int i = 0; i < list.Count; i++)
            {
                var data = list[i];
                if (!attrDic.TryAdd(data.attributes_type, data))
                {
                    attrDic[data.attributes_type] = data;
                }
            }
            var curNum = 0;
            var maxNum = attrDic[attributes_type.attributes_type_51]?.value ?? 0;
            var ratio = (float)curNum / maxNum;

            string stateStr;
            if (ratio <= 0) { stateStr = ToolScriptExtend.GetLang(1100439); }
            else if (ratio >= 1) { stateStr = ToolScriptExtend.GetLang(1100436); }
            else
            {
                var isFire = true;
                stateStr = isFire ? ToolScriptExtend.GetLang(1100438) : ToolScriptExtend.GetLang(1100437);
            }
            m_txtState.text = ToolScriptExtend.GetLang(1100435) + stateStr;

            m_imgProgress.rectTransform.sizeDelta = new Vector2(860 * ratio, 40);
            m_txtProgress.text = $"{ToolScriptExtend.FormatNumberWithSeparator(curNum)}/{ToolScriptExtend.FormatNumberWithSeparator(maxNum)}";
            m_txtNum.text = "0";

            m_btnSet.gameObject.SetActive(ratio < 1);

            defendTime = 0;
            m_txtTime.text = defendTime > 0 ? TimeHelper.ToDateTimeText(defendTime) : "";

            OnUpdateTeam();
        }

        private void OnUpdateTeam()
        {
            teamTypeList.Clear();
            extraList.Clear();

            teamList = GameEntry.LogicData.TeamData.defendTeamList;
            for (int i = 0; i < teamList.Count; i++)
            {
                if (teamList[i].Index != 0) { teamTypeList.Add(teamList[i].TeamType); }
                else { extraList.Add(teamList[i].TeamType); }
            }

            var firstTeamType = (int)Team.TeamType.Common1;
            for (int i = 0; i < 4; i++)
            {
                var teamType = (Team.TeamType)(firstTeamType + i);
                if (!teamTypeList.Contains(teamType) && !extraList.Contains(teamType))
                {
                    teamList.Add(new DefendTeam()
                    {
                        Index = 0,
                        TeamType = teamType,
                    });
                    extraList.Add(teamType);
                }
            }

            if (m_TableViewV.itemPrototype) { m_TableViewV.ReloadData(); }
            else { m_TableViewV.InitTableViewByIndex(0); }
        }

        private void OnUpdateItem(int index, GameObject go)
        {
            var rootTrans = go.transform.Find("bg");
            var bg = rootTrans.GetComponent<UIImage>();
            var rankTxt = rootTrans.Find("rankTxt").GetComponent<UIText>();
            var nameTxt = rootTrans.Find("nameTxt").GetComponent<UIText>();
            var icon = rootTrans.Find("nameTxt/icon").GetComponent<UIImage>();
            var downBtn = rootTrans.Find("downBtn").GetComponent<UIButton>();
            var downBtnImg = rootTrans.Find("downBtn").GetComponent<UIImage>();
            var upBtn = rootTrans.Find("upBtn").GetComponent<UIButton>();
            var upBtnImg = rootTrans.Find("upBtn").GetComponent<UIImage>();
            var powerTxt = rootTrans.Find("powerTxt").GetComponent<UIText>();
            var joinTog = rootTrans.Find("joinTog").GetComponent<UIToggle>();

            var data = teamList[index];
            var showIndex = index + 1;
            rankTxt.text = showIndex.ToString();
            nameTxt.text = ToolScriptExtend.GetLang(1100430 + (int)data.TeamType);
            icon.SetImage("Sprite/ui_jianzhu_duilie/jianzhu_duilie_touxiangk_jiaobiao1.png");

            var teamData = GameEntry.LogicData.TeamData.GetTeam(data.TeamType);
            Dictionary<int, HeroModule> heroVoDic = new();
            ulong powerNum = 0;
            if (teamData != null)
            {
                foreach (var item in teamData)
                {
                    var heroVo = GameEntry.LogicData.HeroData.GetHeroModule((itemid)item.HeroId);
                    heroVoDic[item.Pos] = heroVo;
                    powerNum += heroVo.power;
                }
            }
            powerTxt.text = ToolScriptExtend.FormatNumberWithSeparator(powerNum);

            var isDefend = data.Index != 0;
            if (isDefend)
            {
                var maxNum = teamTypeList.Count;
                var isShow = showIndex < maxNum;
                downBtn.enabled = isShow;
                downBtnImg.SetImage(isShow ? "Sprite/ui_jianzhu_chengqiang/button_small4_xia.png" : "Sprite/ui_jianzhu_chengqiang/button_small4_xia1.png");

                isShow = showIndex > 1;
                upBtn.enabled = isShow;
                upBtnImg.SetImage(isShow ? "Sprite/ui_jianzhu_chengqiang/button_small4_shang.png" : "Sprite/ui_jianzhu_chengqiang/button_small4_shang1.png");
            }
            downBtn.gameObject.SetActive(isDefend);
            upBtn.gameObject.SetActive(isDefend);

            downBtn.onClick.RemoveAllListeners();
            downBtn.onClick.AddListener(() =>
            {
                var teamType = teamTypeList[index];
                teamTypeList[index] = teamTypeList[index + 1];
                teamTypeList[index + 1] = teamType;
                OnSetTeam();
            });

            upBtn.onClick.RemoveAllListeners();
            upBtn.onClick.AddListener(() =>
            {
                var teamType = teamTypeList[index];
                teamTypeList[index] = teamTypeList[index - 1];
                teamTypeList[index - 1] = teamType;
                OnSetTeam();
            });

            joinTog.onValueChanged.RemoveAllListeners();
            joinTog.isOn = isDefend;
            joinTog.onValueChanged.AddListener((isOn) =>
            {
                var teamType = data.TeamType;
                if (isOn)
                {
                    teamTypeList.Add(teamType);
                    extraList.Remove(teamType);
                }
                else
                {
                    teamTypeList.Remove(teamType);
                    extraList.Add(teamType);
                }

                OnSetTeam();
            });

            var tableViewH = rootTrans.Find("tableViewH").GetComponent<Mosframe.TableViewH>();
            var heroItem = rootTrans.Find("heroItem").gameObject;

            tableViewH.GetItemCount = () => 5;
            tableViewH.GetItemGo = () => heroItem;
            tableViewH.UpdateItemCell = (idx, item) =>
            {
                var heroItem = item.transform.Find("UIHeroItem").GetComponent<UIHeroItem>();
                var maskBg = item.transform.Find("maskBg").gameObject;

                heroVoDic.TryGetValue(idx + 1, out var heroVo);
                var isShow = heroVo != null;
                if (isShow) heroItem.Refresh(heroVo);
                heroItem.gameObject.SetActive(isShow);
                maskBg.SetActive(!isShow);
            };

            if (tableViewH.itemPrototype) { tableViewH.ReloadData(); }
            else { tableViewH.InitTableViewByIndex(0); }
        }

        private void OnShowTip(int langId, Vector3 pos)
        {
            m_btnTipMask.gameObject.SetActive(true);

            m_txtTip.text = ToolScriptExtend.GetLang(langId);

            m_goTip.transform.position = pos;
            m_goTip.transform.AddLocalPositionX(-20);
            m_goTip.transform.AddLocalPositionY(110);

            var rect = m_goTip.GetComponent<RectTransform>();
            LayoutRebuilder.ForceRebuildLayoutImmediate(rect);
        }

        private void OnSetTeam()
        {
            List<DefendTeam> list = new();
            for (int i = 0; i < teamTypeList.Count; i++)
            {
                list.Add(new DefendTeam()
                {
                    Index = i + 1,
                    TeamType = teamTypeList[i]
                });
            }

            foreach (var item in extraList)
            {
                list.Add(new DefendTeam()
                {
                    Index = 0,
                    TeamType = item
                });
            }

            GameEntry.LogicData.TeamData.TeamDefendModify(list, OnUpdateTeam);
        }

        private void OnBtnExitClick()
        {
            Close();
        }

        private void OnBtnTitleClick()
        {
            OnShowTip(1100447, m_btnTitle.transform.position);
        }

        private void OnBtnSetClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UICommonConfirmForm, new DialogParams
            {
                Title = ToolScriptExtend.GetLang(1100147),
                Content = ToolScriptExtend.GetLang(1100449),
                ConfirmText = ToolScriptExtend.GetLang(1100144),
                CancelText = ToolScriptExtend.GetLang(1100143),
                OnClickConfirm = (a) =>
                {
                    // 修复耐久度
                },
            });
        }

        private void OnBtnTipClick()
        {
            OnShowTip(1100448, m_btnTip.transform.position);
        }

        private void OnBtnTipMaskClick()
        {
            if (m_btnTipMask.gameObject.activeSelf)
                m_btnTipMask.gameObject.SetActive(false);
        }
    }
}
