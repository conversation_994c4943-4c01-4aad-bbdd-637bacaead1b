using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIRecruitRatioForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnBack;
        [SerializeField] private UIButton m_btnClose;

        [SerializeField] private ScrollRect m_scrollviewMain;

        [SerializeField] private GameObject m_goGroup;
        [SerializeField] private GameObject m_goContent;
        [SerializeField] private GameObject m_goTagItem;
        [SerializeField] private GameObject m_goItem;
        [SerializeField] private GameObject m_goDetailItem;

        void InitBind()
        {
            m_btnBack.onClick.AddListener(OnBtnBackClick);
            m_btnClose.onClick.AddListener(OnBtnCloseClick);
        }
    }
}
