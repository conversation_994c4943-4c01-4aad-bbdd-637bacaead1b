using GameFramework;
using GameFramework.Event;

namespace Game.Hotfix
{
    public class TrainAnimationEventArgs : GameEventArgs
    {
        public static readonly int EventId = typeof(TrainAnimationEventArgs).GetHashCode();
        public override int Id => EventId;
        public TrainAnimationType AnimationType { get; private set; }
        public float Duration { get; private set; }

        public static TrainAnimationEventArgs Create(TrainAnimationType animationType, float duration = 2f)
        {
            TrainAnimationEventArgs eventArgs = ReferencePool.Acquire<TrainAnimationEventArgs>();
            eventArgs.AnimationType = animationType;
            eventArgs.Duration = duration;
            return eventArgs;
        }

        /// <summary>
        /// 清理事件
        /// </summary>
        public override void Clear()
        {
            AnimationType = TrainAnimationType.None;
            Duration = 0f;
        }
    }

    /// <summary>
    /// 火车动画类型
    /// </summary>
    public enum TrainAnimationType
    {
        None,   // 无
        Enter,  // 进站
        Exit,   // 出站
        Stop    // 停止
    }
}
