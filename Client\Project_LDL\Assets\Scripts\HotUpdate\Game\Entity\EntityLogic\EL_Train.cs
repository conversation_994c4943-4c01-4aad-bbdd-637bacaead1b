using GameFramework.Event;
using UnityEngine;

namespace Game.Hotfix
{
    public class ED_Train : EntityData
    {
        public ED_Train(int entityId) : base(entityId)
        {

        }
    }

    public class EL_Train : Entity
    {
        protected override void OnShow(object userData)
        {
            base.OnShow(userData);
        }

        protected override void OnHide(bool isShutdown, object userData)
        {
            base.OnHide(isShutdown, userData);
        }

        public override void OnClick()
        {
            base.OnClick();
            ColorLog.Pink("点击火车");
            GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainStationPlatformForm);
        }
    }
}