using UnityEngine;
using DG.Tweening;
using System;

namespace Game.Hotfix
{
    public class ED_Train : EntityData
    {
        public ED_Train(int entityId) : base(entityId)
        {

        }
    }

    public class EL_Train : Entity
    {
        private Transform m_StartPoint;
        private Transform m_StayPoint;
        private Transform m_EndPoint;
        private Tween m_CurrentTween;

        protected override void OnShow(object userData)
        {
            base.OnShow(userData);
        }

        protected override void OnHide(bool isShutdown, object userData)
        {
            base.OnHide(isShutdown, userData);

            // 停止所有动画
            StopAllAnimations();
        }

        /// <summary>
        /// 初始化火车站点位置
        /// </summary>
        /// <param name="startPoint">起始点</param>
        /// <param name="stayPoint">停靠点</param>
        /// <param name="endPoint">终点</param>
        public void InitializeStationPoints(Transform startPoint, Transform stayPoint, Transform endPoint)
        {
            m_StartPoint = startPoint;
            m_StayPoint = stayPoint;
            m_EndPoint = endPoint;

            ColorLog.Green("火车站点初始化完成");
        }

        /// <summary>
        /// 进站动画：从起始点移动到停靠点
        /// </summary>
        /// <param name="duration">动画时长</param>
        /// <param name="onComplete">动画完成回调</param>
        public void PlayEnterStationAnimation(float duration = 2f, Action onComplete = null)
        {
            if (m_StartPoint == null || m_StayPoint == null)
            {
                ColorLog.Red("火车站点未初始化，无法播放进站动画");
                onComplete?.Invoke();
                return;
            }

            // 停止当前动画
            StopCurrentAnimation();

            // 设置起始位置
            transform.position = m_StartPoint.position;

            // 创建进站动画序列
            Sequence enterSequence = DOTween.Sequence();

            // 第一段：缓慢启动
            enterSequence.Append(transform.DOMove(Vector3.Lerp(m_StartPoint.position, m_StayPoint.position, 0.3f), duration * 0.4f)
                .SetEase(Ease.OutQuad));

            // 第二段：匀速行驶
            enterSequence.Append(transform.DOMove(Vector3.Lerp(m_StartPoint.position, m_StayPoint.position, 0.8f), duration * 0.4f)
                .SetEase(Ease.Linear));

            // 第三段：减速进站
            enterSequence.Append(transform.DOMove(m_StayPoint.position, duration * 0.2f)
                .SetEase(Ease.InQuad));

            // 设置完成回调
            enterSequence.OnComplete(() =>
            {
                ColorLog.Green("火车进站动画完成");
                onComplete?.Invoke();
            });

            m_CurrentTween = enterSequence;
            ColorLog.Green("开始播放火车进站动画");
        }

        /// <summary>
        /// 出站动画：从停靠点移动到终点
        /// </summary>
        /// <param name="duration">动画时长</param>
        /// <param name="onComplete">动画完成回调</param>
        public void PlayExitStationAnimation(float duration = 2f, Action onComplete = null)
        {
            if (m_StayPoint == null || m_EndPoint == null)
            {
                ColorLog.Red("火车站点未初始化，无法播放出站动画");
                onComplete?.Invoke();
                return;
            }

            // 停止当前动画
            StopCurrentAnimation();

            // 确保在停靠点位置
            transform.position = m_StayPoint.position;

            // 创建出站动画序列
            Sequence exitSequence = DOTween.Sequence();

            // 第一段：缓慢启动
            exitSequence.Append(transform.DOMove(Vector3.Lerp(m_StayPoint.position, m_EndPoint.position, 0.2f), duration * 0.3f)
                .SetEase(Ease.OutQuad));

            // 第二段：加速离开
            exitSequence.Append(transform.DOMove(Vector3.Lerp(m_StayPoint.position, m_EndPoint.position, 0.7f), duration * 0.4f)
                .SetEase(Ease.InQuad));

            // 第三段：高速驶离
            exitSequence.Append(transform.DOMove(m_EndPoint.position, duration * 0.3f)
                .SetEase(Ease.Linear));

            // 设置完成回调
            exitSequence.OnComplete(() =>
            {
                ColorLog.Green("火车出站动画完成");
                onComplete?.Invoke();
            });

            m_CurrentTween = exitSequence;
            ColorLog.Green("开始播放火车出站动画");
        }

        /// <summary>
        /// 停止当前动画
        /// </summary>
        public void StopCurrentAnimation()
        {
            if (m_CurrentTween != null && m_CurrentTween.IsActive())
            {
                m_CurrentTween.Kill();
                m_CurrentTween = null;
                ColorLog.Yellow("火车动画已停止");
            }
        }

        /// <summary>
        /// 停止所有动画
        /// </summary>
        private void StopAllAnimations()
        {
            StopCurrentAnimation();
            transform.DOKill(); // 停止所有与此Transform相关的DOTween动画
        }

        /// <summary>
        /// 瞬间移动到指定站点
        /// </summary>
        /// <param name="stationType">站点类型</param>
        public void TeleportToStation(TrainStationType stationType)
        {
            StopCurrentAnimation();

            Vector3 targetPosition = stationType switch
            {
                TrainStationType.Start => m_StartPoint != null ? m_StartPoint.position : transform.position,
                TrainStationType.Stay => m_StayPoint != null ? m_StayPoint.position : transform.position,
                TrainStationType.End => m_EndPoint != null ? m_EndPoint.position : transform.position,
                _ => transform.position
            };

            transform.position = targetPosition;
            ColorLog.Green($"火车瞬移到{stationType}站点");
        }

        public override void OnClick()
        {
            base.OnClick();
            ColorLog.Pink("点击火车");
            GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainStationPlatformForm);
        }
    }

    /// <summary>
    /// 火车站点类型
    /// </summary>
    public enum TrainStationType
    {
        Start,  // 起始点
        Stay,   // 停靠点
        End     // 终点
    }
}