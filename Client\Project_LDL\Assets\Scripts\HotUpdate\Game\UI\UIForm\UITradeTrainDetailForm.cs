using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;
using Game.Hotfix.Config;
using GameFramework.Resource;
using Mosframe;

namespace Game.Hotfix
{
    public partial class UITradeTrainDetailForm : UGuiFormEx
    {
        UITrainItem curSelectedTrain;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            InitPanel();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            if (userData is UITrainItem train)
            {
                curSelectedTrain = train;

                if (train.isOther)
                {
                    m_btnPlunder.gameObject.SetActive(true);
                    m_btnShare.gameObject.SetActive(true);
                    m_btnBattleRecord.gameObject.SetActive(false);
                }
                else
                {
                    m_btnPlunder.gameObject.SetActive(false);
                    m_btnShare.gameObject.SetActive(false);
                    m_btnBattleRecord.gameObject.SetActive(true);
                }
            }

            RefreshPanel();
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        protected override void OnUpdate(float elapseSeconds, float realElapseSeconds)
        {
            base.OnUpdate(elapseSeconds, realElapseSeconds);

            if (curSelectedTrain == null
            || curSelectedTrain.trainData == null
            || curSelectedTrain.trainData.Train == null) return;
            long remainTime = curSelectedTrain.trainData.Train.PrepareTime - (long)TimeComponent.Now;
            if (remainTime < 0)
            {
                m_txtTime.text = TimeHelper.FormatGameTimeWithDays(0);
            }
            else
            {
                m_txtTime.text = TimeHelper.FormatGameTimeWithDays((int)remainTime);
            }
        }

        private void OnBtnBackClick()
        {
            Close();
        }

        private void OnBtnPlunderClick()
        {
            Close();
            GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainBattlePlanForm);
        }

        private void OnBtnShareClick()
        {

        }

        private void OnBtnBattleRecordClick()
        {
            if (GameEntry.TradeTruckData.myTrain == null) return;
            GameEntry.TradeTruckData.RequestTrainFightRecordList(GameEntry.TradeTruckData.myTrain.Id, (result) =>
            {
                ColorLog.Pink("火车战斗记录", result);
                GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainBattleRecordForm, result);
            });
        }

        void InitPanel()
        {
            foreach (Transform item in m_transCarriage)
            {
                UIButton btn = item.Find("btn").GetComponent<UIButton>();
                btn.onClick.RemoveAllListeners();
                btn.onClick.AddListener(() =>
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainPassengerForm);
                });
            }
        }

        void RefreshPanel()
        {
            RefreshPlunderCount();
            RefreshTeam();

            if (curSelectedTrain == null || curSelectedTrain.trainData == null) return;
            var data = curSelectedTrain.trainData;

            int index = 0;
            foreach (Transform item in m_transRewardParent)
            {
                if (index < data.Boxcar.Count)
                {
                    Trade.TradeBoxcar tradeBoxcar = data.Boxcar[index];
                    if (tradeBoxcar != null)
                    {
                        if (tradeBoxcar.Goods.Count > 0)
                        {
                            Transform grid = item.Find("grid");
                            List<Trade.TradeGoods> rewards = new(tradeBoxcar.Goods);
                            bool needEffect = index > 0;
                            RefreshReward(rewards, grid, m_transReward, needEffect);
                        }
                        UIImage border = item.Find("border").GetComponent<UIImage>();
                        border.SetImage(GetBorder(tradeBoxcar.Quality));
                    }
                }
                index++;
            }

            int index2 = 1;
            foreach (Transform item in m_transCarriage)
            {
                if (index2 < data.Boxcar.Count)
                {
                    Trade.TradeBoxcar tradeBoxcar = data.Boxcar[index2];
                    if (tradeBoxcar != null)
                    {
                        UIImage carriage = item.GetComponent<UIImage>();
                        carriage.SetImage(GetCarriage(tradeBoxcar.Quality));

                        List<Trade.TradePassenger> tradePassenger = new(tradeBoxcar.Passengers);
                        int count = tradePassenger.Count;
                        UIText txtPassenger = item.Find("btn/Text").GetComponent<UIText>();
                        txtPassenger.text = $"{count}/5";

                        UIButton btn = item.Find("btn").GetComponent<UIButton>();
                        btn.onClick.RemoveAllListeners();
                        btn.onClick.AddListener(() =>
                        {
                            GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainPassengerForm, tradePassenger);
                        });
                    }
                }
                index2++;
            }

            GameEntry.RoleData.RequestRoleQueryLocalSingle(data.RoleId, (roleBrief) =>
            {
                ColorLog.Pink("查询列车长信息", roleBrief);
                if (roleBrief != null)
                {
                    m_txtLevel.text = $"Level.{roleBrief.Level}";
                    m_txtName.text = roleBrief.Name;
                    m_txtPower.text = ToolScriptExtend.FormatNumberWithSeparator(roleBrief.Power);
                }
            });
        }

        void RefreshPlunderCount()
        {
            int count = GameEntry.TradeTruckData.TruckPlunderTodayCount;
            int countMax = 4;
            if (count >= countMax) count = countMax;
            m_txtTodayPlunderCount.text = $"{ToolScriptExtend.GetLang(1115)}{count}/{countMax}";
        }

        void RefreshReward(List<Trade.TradeGoods> rewards, Transform transContentReward, Transform transReward, bool needEffect = true)
        {
            foreach (Transform item in transContentReward)
            {
                item.gameObject.SetActive(false);
            }

            for (int i = 0; i < rewards.Count; i++)
            {
                if (i < transContentReward.childCount)
                {
                    transContentReward.GetChild(i).gameObject.SetActive(true);
                    if (transContentReward.GetChild(i).childCount > 0)
                    {
                        UIItemModule uiItemModule = transContentReward.GetChild(i).GetChild(0).GetComponent<UIItemModule>();
                        if (uiItemModule != null)
                        {
                            uiItemModule.SetData((itemid)rewards[i].Code, rewards[i].Amount);
                            uiItemModule.InitConfigData();
                            uiItemModule.DisplayInfo();
                        }
                    }
                }
                else
                {
                    int j = i;
                    Transform transRewardItem = Instantiate(transReward, transContentReward);
                    BagManager.CreatItem(transRewardItem, (itemid)rewards[i].Code, rewards[i].Amount, (item) =>
                    {
                        item.GetComponent<UIButton>().useTween = false;
                        item.SetClick(item.OpenTips);
                        item.transform.Find("count").localScale = new Vector2(1.6f, 1.6f);
                        if (j == 0 && needEffect)
                        {
                            GameObject effect = Instantiate(m_goItemEffect, transRewardItem);
                            effect.transform.localScale = new Vector3(2f, 2f, 1f);
                            effect.SetActive(true);
                        }
                    });
                }
            }
        }

        void RefreshTeam()
        {
            Transform teamParent = m_transTeam;
            
            foreach (Transform item in teamParent)
            {
                int index = int.Parse(item.gameObject.name.Substring(item.gameObject.name.Length - 1, 1));
                Team.TeamType teamType = Team.TeamType.TradeTrainDefend1;
                if (index == 1) teamType = Team.TeamType.TradeTrainDefend1;
                else if (index == 2) teamType = Team.TeamType.TradeTrainDefend2;
                else if (index == 3) teamType = Team.TeamType.TradeTrainDefend3;

                List<Fight.FormationHero> teamData = GameEntry.LogicData.TeamData.GetTeam(teamType);

                UIText txtPower = item.Find("txtPower").GetComponent<UIText>();
                txtPower.gameObject.SetActive(false);
                txtPower.text = string.Empty;

                if (teamData != null)
                {
                    ulong powerTotal = 0;
                    foreach (var data in teamData)
                    {
                        var heroVo = GameEntry.LogicData.HeroData.GetHeroModule((itemid)data.HeroId);
                        powerTotal += heroVo.power;
                    }
                    txtPower.text = ToolScriptExtend.FormatNumberWithUnit(powerTotal);
                    txtPower.gameObject.SetActive(powerTotal > 0);
                }

                for (int i = 0; i < 5; i++)
                {
                    Transform modelParent = item.Find("model" + (i + 1));
                    Transform model = modelParent.Find("model");
                    UIText txtLevel = modelParent.Find("txtLevel").GetComponent<UIText>();
                    Transform starObj = modelParent.Find("starObj");
                    List<UIImage> starList = new();
                    for (int j = 0; j < starObj.childCount; j++)
                    {
                        starList.Add(starObj.GetChild(j).GetComponent<UIImage>());
                    }

                    txtLevel.gameObject.SetActive(false);
                    starObj.gameObject.SetActive(false);

                    txtLevel.text = string.Empty;

                    if (teamData != null)
                    {
                        Fight.FormationHero heroData = null;
                        foreach (var hero in teamData)
                        {
                            if (hero.Pos == i + 1)
                            {
                                heroData = hero;
                            }
                        }

                        if (heroData != null)
                        {
                            var battleRole = GameEntry.LDLTable.GetTableById<battle_role>((int)heroData.HeroId);

                            if (model.transform.childCount > 0)
                            {
                                Destroy(model.transform.GetChild(0).gameObject);
                            }

                            GameEntry.Resource.LoadAsset(battleRole.res_location, new LoadAssetCallbacks(
                            (assetName, asset, duration, userData) =>
                            {
                                var prefab = asset as GameObject;
                                GameObject heroGo = Instantiate(prefab, model);
                                heroGo.setLayer(5);
                                heroGo.transform.localScale = new Vector3(25f, 25f, 25f);
                                heroGo.transform.localRotation = Quaternion.Euler(-90f, 0f, 0f);
                            }));

                            var heroVo = GameEntry.LogicData.HeroData.GetHeroModule((itemid)heroData.HeroId);
                            txtLevel.text = ToolScriptExtend.GetLangFormat(80000135, heroVo.level.ToString());
                            txtLevel.gameObject.SetActive(true);

                            var starNum = heroVo.StarNum;
                            var starOrder = heroVo.StarOrder;
                            for (int j = 0; j < starList.Count; j++)
                            {
                                var starSp = starList[j];
                                string pathStr;
                                if (j < starNum)
                                {
                                    pathStr = "Sprite/ui_hero/hero_icon_star5.png";
                                }
                                else if (j < starNum + 1 && starOrder > 0)
                                {
                                    pathStr = string.Format("Sprite/ui_hero/hero_icon_star{0}.png", starOrder);
                                }
                                else
                                {
                                    pathStr = "Sprite/ui_hero/hero_icon_star0.png";
                                }

                                starSp.SetImage(pathStr);
                            }
                            starObj.gameObject.SetActive(true);
                        }
                        else
                        {
                            if (model.transform.childCount > 0)
                            {
                                Destroy(model.transform.GetChild(0).gameObject);
                            }
                        }
                    }
                    else
                    {
                        if (model.transform.childCount > 0)
                        {
                            Destroy(model.transform.GetChild(0).gameObject);
                        }
                    }
                }
            }
        }

        string GetBorder(PbGameconfig.car_quality quality)
        {
            return quality switch
            {
                PbGameconfig.car_quality._1 => "Sprite/ui_maoyi/maoyi_huoche_kapian1.png",
                PbGameconfig.car_quality._2 => "Sprite/ui_maoyi/maoyi_huoche_kapian2.png",
                PbGameconfig.car_quality._3 => "Sprite/ui_maoyi/maoyi_huoche_kapian3.png",
                PbGameconfig.car_quality._4 => "Sprite/ui_maoyi/maoyi_huoche_kapian4.png",
                PbGameconfig.car_quality._5 => "Sprite/ui_maoyi/maoyi_huoche_kapian5.png",
                _ => "Sprite/ui_maoyi/maoyi_huoche_kapian1.png",
            };
        }

        string GetCarriage(PbGameconfig.car_quality quality)
        {
            return quality switch
            {
                PbGameconfig.car_quality._1 => "Sprite/ui_maoyi/maoyi_huoche_chexiang1.png",
                PbGameconfig.car_quality._2 => "Sprite/ui_maoyi/maoyi_huoche_chexiang2.png",
                PbGameconfig.car_quality._3 => "Sprite/ui_maoyi/maoyi_huoche_chexiang3.png",
                PbGameconfig.car_quality._4 => "Sprite/ui_maoyi/maoyi_huoche_chexiang4.png",
                PbGameconfig.car_quality._5 => "Sprite/ui_maoyi/maoyi_huoche_chexiang5.png",
                _ => "Sprite/ui_maoyi/maoyi_huoche_chexiang1.png",
            };
        }
    }
}
