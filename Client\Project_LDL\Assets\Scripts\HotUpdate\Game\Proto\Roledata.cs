// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: roledata.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Roledata {

  /// <summary>Holder for reflection information generated from roledata.proto</summary>
  public static partial class RoledataReflection {

    #region Descriptor
    /// <summary>File descriptor for roledata.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static RoledataReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Cg5yb2xlZGF0YS5wcm90bxIIcm9sZWRhdGEaC2J1aWxkLnByb3RvGgtlcXVp",
            "cC5wcm90bxoLZmlnaHQucHJvdG8aCmhlcm8ucHJvdG8aCml0ZW0ucHJvdG8a",
            "D3ByaXZpbGVnZS5wcm90bxoOc3Vydml2b3IucHJvdG8aDHdvcmtlci5wcm90",
            "bxoKdGFzay5wcm90bxoJdWF2LnByb3RvGgp0ZWNoLnByb3RvGgt0cmFkZS5w",
            "cm90byIuCghSb2xlTWV0YRIRCglzZXJ2ZXJfaWQYASABKA0SDwoHcm9sZV9p",
            "ZBgCIAEoBCKvBAoJUm9sZUJyaWVmEhEKCXNlcnZlcl9pZBgBIAEoDRITCgtz",
            "ZXJ2ZXJfbmFtZRgCIAEoCRIKCgJpZBgDIAEoBBIMCgRuYW1lGAQgASgJEg0K",
            "BWxldmVsGAUgASgNEhgKEGlzX2N1c3RvbV9hdmF0YXIYBiABKAgSGgoSaGVh",
            "ZF9zeXN0ZW1fYXZhdGFyGAcgASgNEhoKEmhlYWRfY3VzdG9tX2F2YXRhchgI",
            "IAEoCRITCgtoZWFkX2JvcmRlchgJIAEoDRINCgVwb3dlchgKIAEoBBITCgts",
            "ZWFndWVfbmFtZRgLIAEoCRIMCgRsYW5nGAwgASgFEg4KBmdlbmRlchgNIAEo",
            "BRIRCgljcmVhdGVfYXQYDiABKAMSEAoIbG9naW5fYXQYDyABKAMSEQoJbG9n",
            "b3V0X2F0GBAgASgDEhAKCHVuaW9uX2lkGBEgASgEEhYKDnVuaW9uX3Bvc2l0",
            "aW9uGBIgASgFEhgKEHVuaW9uX3Blcm1pc3Npb24YEyABKAUSGAoQdW5pb25f",
            "bGVhdmVfdGltZRgUIAEoAxIVCg1pc19vcGVuX3VuaW9uGBUgASgIEhsKE2lz",
            "X2ZpcnN0X2pvaW5fdW5pb24YFiABKAgSGQoRaGVhZHF1YXJ0ZXJzTGV2ZWwY",
            "FyABKA0SFwoPdW5pb25fam9pbl90aW1lGBggASgDEhcKD2tpbGxfc29sZGVy",
            "X251bRgZIAEoBBIRCgl2aXBfbGV2ZWwYGiABKAUisAUKCFJvbGVJbmZvEgoK",
            "AmlkGAEgASgEEiAKBGF0dHIYAiABKAsyEi5yb2xlZGF0YS5Sb2xlQXR0chIi",
            "CgVidWlsZBgDIAEoCzITLnJvbGVkYXRhLlJvbGVCdWlsZBIiCgV1bmlvbhgE",
            "IAEoCzITLnJvbGVkYXRhLlJvbGVVbmlvbhIgCgRpdGVtGAUgASgLMhIucm9s",
            "ZWRhdGEuUm9sZUl0ZW0SIAoEaGVybxgGIAEoCzISLnJvbGVkYXRhLlJvbGVI",
            "ZXJvEiIKBWVxdWlwGAcgASgLMhMucm9sZWRhdGEuUm9sZUVxdWlwEiIKBXF1",
            "ZXVlGAggASgLMhMucm9sZWRhdGEuUm9sZVF1ZXVlEioKB3dvcmtlcnMYCSAB",
            "KAsyGS5yb2xlZGF0YS5Sb2xlQnVpbGRXb3JrZXISJwoIc29sZGllcnMYCiAB",
            "KAsyFS5yb2xlZGF0YS5Sb2xlU29sZGllchIoCglob3NwaXRhbHMYCyABKAsy",
            "FS5yb2xlZGF0YS5Sb2xlU29sZGllchIpCglzdXJ2aXZvcnMYDCABKAsyFi5y",
            "b2xlZGF0YS5Sb2xlU3Vydml2b3ISFQoDdWF2GA0gASgLMggudWF2LlVBVhIh",
            "CgV0ZWNocxgOIAEoCzISLnJvbGVkYXRhLlJvbGVUZWNoEiEKBXRlYW1zGBUg",
            "ASgLMhIucm9sZWRhdGEuUm9sZVRlYW0SJwoIZHVuZ2VvbnMYFiABKAsyFS5y",
            "b2xlZGF0YS5Sb2xlRHVuZ2VvbhIhCgV0YXNrcxgXIAEoCzISLnJvbGVkYXRh",
            "LlJvbGVUYXNrEiIKBXRyYWRlGBggASgLMhMucm9sZWRhdGEuUm9sZVRyYWRl",
            "EisKCnByaXZpbGVnZXMYGSABKAsyFy5yb2xlZGF0YS5Sb2xlUHJpdmlsZWdl",
            "IqUECghSb2xlQXR0chIRCglzZXJ2ZXJfaWQYASABKA0SEwoLc2VydmVyX25h",
            "bWUYAiABKAkSDAoEbmFtZRgDIAEoCRIYChBpc19jdXN0b21fYXZhdGFyGAQg",
            "ASgIEhoKEmhlYWRfc3lzdGVtX2F2YXRhchgFIAEoDRIaChJoZWFkX2N1c3Rv",
            "bV9hdmF0YXIYBiABKAkSEwoLaGVhZF9ib3JkZXIYByABKA0SDQoFcG93ZXIY",
            "CCABKAQSFQoNbGFzdF9sb2dpbl9hdBgJIAEoAxIWCg5sYXN0X2xvZ291dF9h",
            "dBgKIAEoAxIRCgljcmVhdGVfYXQYCyABKAMSDAoEbGFuZxgMIAEoBRIOCgZn",
            "ZW5kZXIYDSABKA0SFwoPa2lsbF9zb2xkZXJfbnVtGA4gASgEEhoKEmlubmVy",
            "X2NpdHlfZ3JpZF9pZBgPIAEoBRIcChRpbm5lcl9jaXR5X3JlZ2lvbl9pZBgQ",
            "IAEoBRIRCgl2aXBfbGV2ZWwYESABKAUSDwoHdmlwX2V4cBgSIAEoBRIUCgx2",
            "aXBfZW5kX3RpbWUYEyABKAMSIgoaaXNfdmlwX2RhaWx5X3BvaW50X3JlY2Vp",
            "dmUYFCABKAgSIQoZaXNfdmlwX2RhaWx5X2dpZnRfcmVjZWl2ZRgVIAEoCBIc",
            "ChR2aXBfdG9kYXlfYnV5X3BvaW50cxgWIAEoBRIbChNsb2dpbl9jb250aW51",
            "ZV9kYXlzGBcgASgFIikKCVJvbGVCdWlsZBIcCgZidWlsZHMYASADKAsyDC5i",
            "dWlsZC5CdWlsZCLRAQoJUm9sZVVuaW9uEhAKCHVuaW9uX2lkGAEgASgEEhYK",
            "DnVuaW9uX3Bvc2l0aW9uGAIgASgFEhgKEHVuaW9uX3Blcm1pc3Npb24YAyAB",
            "KAUSGAoQdW5pb25fbGVhdmVfdGltZRgEIAEoAxIbChNpc19maXJzdF9qb2lu",
            "X3VuaW9uGAUgASgIEhkKEXNob3dfc3BlY2lhbF92aWV3GAYgASgIEhUKDWlz",
            "X29wZW5fdW5pb24YByABKAgSFwoPdW5pb25fam9pbl90aW1lGAggASgDIiUK",
            "CFJvbGVJdGVtEhkKBWl0ZW1zGAEgAygLMgouaXRlbS5JdGVtIiYKCFJvbGVI",
            "ZXJvEhoKBmhlcm9lcxgBIAMoCzIKLmhlcm8uSGVybyIpCglSb2xlRXF1aXAS",
            "HAoGZXF1aXBzGAEgAygLMgwuZXF1aXAuRXF1aXAiKQoJUm9sZVF1ZXVlEhwK",
            "BnF1ZXVlcxgBIAMoCzIMLmJ1aWxkLlF1ZXVlIjIKD1JvbGVCdWlsZFdvcmtl",
            "chIfCgd3b3JrZXJzGAEgAygLMg4ud29ya2VyLldvcmtlciIvCgtSb2xlU29s",
            "ZGllchIgCghzb2xkaWVycxgBIAMoCzIOLmJ1aWxkLlNvbGRpZXIiNQoMUm9s",
            "ZVN1cnZpdm9yEiUKCXN1cnZpdm9ycxgBIAMoCzISLnN1cnZpdm9yLlN1cnZp",
            "dm9yIlgKCFJvbGVUZWFtEiMKBXRlYW1zGAEgAygLMhQuZmlnaHQuRm9ybWF0",
            "aW9uVGVhbRInCgxkZWZlbmRfdGVhbXMYAiADKAsyES5maWdodC5EZWZlbmRU",
            "ZWFtIkAKC1JvbGVEdW5nZW9uEhIKCmR1bmdlb25faWQYASABKAUSHQoVYWNj",
            "dW11bGF0ZWRfcmV3YXJkX2F0GAIgASgDIi0KCFJvbGVUYXNrEiEKCXRhc2tf",
            "bGlzdBgBIAMoCzIOLnRhc2suVGFza0xpc3QiJQoIUm9sZVRlY2gSGQoFdGVj",
            "aHMYASADKAsyCi50ZWNoLlRlY2gikQEKCVJvbGVUcmFkZRIsCgh2YW5fbGlz",
            "dBgBIAMoCzIaLnRyYWRlLlRyYWRlQ2FyZ29UcmFuc3BvcnQSGQoRdG9kYXlf",
            "dHJhZGVfdGltZXMYAiABKAUSFwoPdG9kYXlfcm9iX3RpbWVzGAMgASgFEiIK",
            "GmJ1eV9leHByZXNzX2NvbnRyYWN0X3RpbWVzGAQgASgFIjMKDVJvbGVQcml2",
            "aWxlZ2USIgoEbGlzdBgBIAMoCzIULnByaXZpbGVnZS5Qcml2aWxlZ2VCG1oZ",
            "c2VydmVyL2FwaS9wYi9wYl9yb2xlZGF0YWIGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Build.BuildReflection.Descriptor, global::Equip.EquipReflection.Descriptor, global::Fight.FightReflection.Descriptor, global::Hero.HeroReflection.Descriptor, global::Item.ItemReflection.Descriptor, global::Privilege.PrivilegeReflection.Descriptor, global::Survivor.SurvivorReflection.Descriptor, global::Worker.WorkerReflection.Descriptor, global::Task.TaskReflection.Descriptor, global::Uav.UavReflection.Descriptor, global::Tech.TechReflection.Descriptor, global::Trade.TradeReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Roledata.RoleMeta), global::Roledata.RoleMeta.Parser, new[]{ "ServerId", "RoleId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Roledata.RoleBrief), global::Roledata.RoleBrief.Parser, new[]{ "ServerId", "ServerName", "Id", "Name", "Level", "IsCustomAvatar", "HeadSystemAvatar", "HeadCustomAvatar", "HeadBorder", "Power", "LeagueName", "Lang", "Gender", "CreateAt", "LoginAt", "LogoutAt", "UnionId", "UnionPosition", "UnionPermission", "UnionLeaveTime", "IsOpenUnion", "IsFirstJoinUnion", "HeadquartersLevel", "UnionJoinTime", "KillSolderNum", "VipLevel" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Roledata.RoleInfo), global::Roledata.RoleInfo.Parser, new[]{ "Id", "Attr", "Build", "Union", "Item", "Hero", "Equip", "Queue", "Workers", "Soldiers", "Hospitals", "Survivors", "Uav", "Techs", "Teams", "Dungeons", "Tasks", "Trade", "Privileges" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Roledata.RoleAttr), global::Roledata.RoleAttr.Parser, new[]{ "ServerId", "ServerName", "Name", "IsCustomAvatar", "HeadSystemAvatar", "HeadCustomAvatar", "HeadBorder", "Power", "LastLoginAt", "LastLogoutAt", "CreateAt", "Lang", "Gender", "KillSolderNum", "InnerCityGridId", "InnerCityRegionId", "VipLevel", "VipExp", "VipEndTime", "IsVipDailyPointReceive", "IsVipDailyGiftReceive", "VipTodayBuyPoints", "LoginContinueDays" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Roledata.RoleBuild), global::Roledata.RoleBuild.Parser, new[]{ "Builds" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Roledata.RoleUnion), global::Roledata.RoleUnion.Parser, new[]{ "UnionId", "UnionPosition", "UnionPermission", "UnionLeaveTime", "IsFirstJoinUnion", "ShowSpecialView", "IsOpenUnion", "UnionJoinTime" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Roledata.RoleItem), global::Roledata.RoleItem.Parser, new[]{ "Items" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Roledata.RoleHero), global::Roledata.RoleHero.Parser, new[]{ "Heroes" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Roledata.RoleEquip), global::Roledata.RoleEquip.Parser, new[]{ "Equips" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Roledata.RoleQueue), global::Roledata.RoleQueue.Parser, new[]{ "Queues" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Roledata.RoleBuildWorker), global::Roledata.RoleBuildWorker.Parser, new[]{ "Workers" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Roledata.RoleSoldier), global::Roledata.RoleSoldier.Parser, new[]{ "Soldiers" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Roledata.RoleSurvivor), global::Roledata.RoleSurvivor.Parser, new[]{ "Survivors" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Roledata.RoleTeam), global::Roledata.RoleTeam.Parser, new[]{ "Teams", "DefendTeams" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Roledata.RoleDungeon), global::Roledata.RoleDungeon.Parser, new[]{ "DungeonId", "AccumulatedRewardAt" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Roledata.RoleTask), global::Roledata.RoleTask.Parser, new[]{ "TaskList" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Roledata.RoleTech), global::Roledata.RoleTech.Parser, new[]{ "Techs" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Roledata.RoleTrade), global::Roledata.RoleTrade.Parser, new[]{ "VanList", "TodayTradeTimes", "TodayRobTimes", "BuyExpressContractTimes" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Roledata.RolePrivilege), global::Roledata.RolePrivilege.Parser, new[]{ "List" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  /// <summary>
  /// RoleMeta 一个角色的元数据
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RoleMeta : pb::IMessage<RoleMeta>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RoleMeta> _parser = new pb::MessageParser<RoleMeta>(() => new RoleMeta());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RoleMeta> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Roledata.RoledataReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleMeta() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleMeta(RoleMeta other) : this() {
      serverId_ = other.serverId_;
      roleId_ = other.roleId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleMeta Clone() {
      return new RoleMeta(this);
    }

    /// <summary>Field number for the "server_id" field.</summary>
    public const int ServerIdFieldNumber = 1;
    private uint serverId_;
    /// <summary>
    /// 区服 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint ServerId {
      get { return serverId_; }
      set {
        serverId_ = value;
      }
    }

    /// <summary>Field number for the "role_id" field.</summary>
    public const int RoleIdFieldNumber = 2;
    private ulong roleId_;
    /// <summary>
    /// 角色 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong RoleId {
      get { return roleId_; }
      set {
        roleId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RoleMeta);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RoleMeta other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ServerId != other.ServerId) return false;
      if (RoleId != other.RoleId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ServerId != 0) hash ^= ServerId.GetHashCode();
      if (RoleId != 0UL) hash ^= RoleId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ServerId != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(ServerId);
      }
      if (RoleId != 0UL) {
        output.WriteRawTag(16);
        output.WriteUInt64(RoleId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ServerId != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(ServerId);
      }
      if (RoleId != 0UL) {
        output.WriteRawTag(16);
        output.WriteUInt64(RoleId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ServerId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(ServerId);
      }
      if (RoleId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(RoleId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RoleMeta other) {
      if (other == null) {
        return;
      }
      if (other.ServerId != 0) {
        ServerId = other.ServerId;
      }
      if (other.RoleId != 0UL) {
        RoleId = other.RoleId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            ServerId = input.ReadUInt32();
            break;
          }
          case 16: {
            RoleId = input.ReadUInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            ServerId = input.ReadUInt32();
            break;
          }
          case 16: {
            RoleId = input.ReadUInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// RoleBrief 角色简略数据，查询玩家信息时使用
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RoleBrief : pb::IMessage<RoleBrief>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RoleBrief> _parser = new pb::MessageParser<RoleBrief>(() => new RoleBrief());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RoleBrief> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Roledata.RoledataReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleBrief() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleBrief(RoleBrief other) : this() {
      serverId_ = other.serverId_;
      serverName_ = other.serverName_;
      id_ = other.id_;
      name_ = other.name_;
      level_ = other.level_;
      isCustomAvatar_ = other.isCustomAvatar_;
      headSystemAvatar_ = other.headSystemAvatar_;
      headCustomAvatar_ = other.headCustomAvatar_;
      headBorder_ = other.headBorder_;
      power_ = other.power_;
      leagueName_ = other.leagueName_;
      lang_ = other.lang_;
      gender_ = other.gender_;
      createAt_ = other.createAt_;
      loginAt_ = other.loginAt_;
      logoutAt_ = other.logoutAt_;
      unionId_ = other.unionId_;
      unionPosition_ = other.unionPosition_;
      unionPermission_ = other.unionPermission_;
      unionLeaveTime_ = other.unionLeaveTime_;
      isOpenUnion_ = other.isOpenUnion_;
      isFirstJoinUnion_ = other.isFirstJoinUnion_;
      headquartersLevel_ = other.headquartersLevel_;
      unionJoinTime_ = other.unionJoinTime_;
      killSolderNum_ = other.killSolderNum_;
      vipLevel_ = other.vipLevel_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleBrief Clone() {
      return new RoleBrief(this);
    }

    /// <summary>Field number for the "server_id" field.</summary>
    public const int ServerIdFieldNumber = 1;
    private uint serverId_;
    /// <summary>
    /// 所在区服 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint ServerId {
      get { return serverId_; }
      set {
        serverId_ = value;
      }
    }

    /// <summary>Field number for the "server_name" field.</summary>
    public const int ServerNameFieldNumber = 2;
    private string serverName_ = "";
    /// <summary>
    /// 所在区服名称
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ServerName {
      get { return serverName_; }
      set {
        serverName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 3;
    private ulong id_;
    /// <summary>
    /// 角色 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int NameFieldNumber = 4;
    private string name_ = "";
    /// <summary>
    /// 角色名称
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "level" field.</summary>
    public const int LevelFieldNumber = 5;
    private uint level_;
    /// <summary>
    /// 角色等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Level {
      get { return level_; }
      set {
        level_ = value;
      }
    }

    /// <summary>Field number for the "is_custom_avatar" field.</summary>
    public const int IsCustomAvatarFieldNumber = 6;
    private bool isCustomAvatar_;
    /// <summary>
    /// 是否是自定义头像
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsCustomAvatar {
      get { return isCustomAvatar_; }
      set {
        isCustomAvatar_ = value;
      }
    }

    /// <summary>Field number for the "head_system_avatar" field.</summary>
    public const int HeadSystemAvatarFieldNumber = 7;
    private uint headSystemAvatar_;
    /// <summary>
    /// 角色头像图标
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint HeadSystemAvatar {
      get { return headSystemAvatar_; }
      set {
        headSystemAvatar_ = value;
      }
    }

    /// <summary>Field number for the "head_custom_avatar" field.</summary>
    public const int HeadCustomAvatarFieldNumber = 8;
    private string headCustomAvatar_ = "";
    /// <summary>
    /// 角色自定义头像链接地址
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string HeadCustomAvatar {
      get { return headCustomAvatar_; }
      set {
        headCustomAvatar_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "head_border" field.</summary>
    public const int HeadBorderFieldNumber = 9;
    private uint headBorder_;
    /// <summary>
    /// 角色头像框
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint HeadBorder {
      get { return headBorder_; }
      set {
        headBorder_ = value;
      }
    }

    /// <summary>Field number for the "power" field.</summary>
    public const int PowerFieldNumber = 10;
    private ulong power_;
    /// <summary>
    /// 总战力
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong Power {
      get { return power_; }
      set {
        power_ = value;
      }
    }

    /// <summary>Field number for the "league_name" field.</summary>
    public const int LeagueNameFieldNumber = 11;
    private string leagueName_ = "";
    /// <summary>
    /// 联盟名称
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string LeagueName {
      get { return leagueName_; }
      set {
        leagueName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "lang" field.</summary>
    public const int LangFieldNumber = 12;
    private int lang_;
    /// <summary>
    /// 玩家当前语言 language_type.xlsx - id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Lang {
      get { return lang_; }
      set {
        lang_ = value;
      }
    }

    /// <summary>Field number for the "gender" field.</summary>
    public const int GenderFieldNumber = 13;
    private int gender_;
    /// <summary>
    /// 性别，0 未知，1 男，2 女
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Gender {
      get { return gender_; }
      set {
        gender_ = value;
      }
    }

    /// <summary>Field number for the "create_at" field.</summary>
    public const int CreateAtFieldNumber = 14;
    private long createAt_;
    /// <summary>
    /// 角色创建时间戳
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long CreateAt {
      get { return createAt_; }
      set {
        createAt_ = value;
      }
    }

    /// <summary>Field number for the "login_at" field.</summary>
    public const int LoginAtFieldNumber = 15;
    private long loginAt_;
    /// <summary>
    /// 最后登录时间戳，秒
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long LoginAt {
      get { return loginAt_; }
      set {
        loginAt_ = value;
      }
    }

    /// <summary>Field number for the "logout_at" field.</summary>
    public const int LogoutAtFieldNumber = 16;
    private long logoutAt_;
    /// <summary>
    /// 最后登出时间戳，秒
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long LogoutAt {
      get { return logoutAt_; }
      set {
        logoutAt_ = value;
      }
    }

    /// <summary>Field number for the "union_id" field.</summary>
    public const int UnionIdFieldNumber = 17;
    private ulong unionId_;
    /// <summary>
    /// 联盟 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong UnionId {
      get { return unionId_; }
      set {
        unionId_ = value;
      }
    }

    /// <summary>Field number for the "union_position" field.</summary>
    public const int UnionPositionFieldNumber = 18;
    private int unionPosition_;
    /// <summary>
    /// 联盟职位
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int UnionPosition {
      get { return unionPosition_; }
      set {
        unionPosition_ = value;
      }
    }

    /// <summary>Field number for the "union_permission" field.</summary>
    public const int UnionPermissionFieldNumber = 19;
    private int unionPermission_;
    /// <summary>
    /// 联盟权限
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int UnionPermission {
      get { return unionPermission_; }
      set {
        unionPermission_ = value;
      }
    }

    /// <summary>Field number for the "union_leave_time" field.</summary>
    public const int UnionLeaveTimeFieldNumber = 20;
    private long unionLeaveTime_;
    /// <summary>
    /// 离开联盟时间戳
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long UnionLeaveTime {
      get { return unionLeaveTime_; }
      set {
        unionLeaveTime_ = value;
      }
    }

    /// <summary>Field number for the "is_open_union" field.</summary>
    public const int IsOpenUnionFieldNumber = 21;
    private bool isOpenUnion_;
    /// <summary>
    /// 是否开启联盟（同盟中心是否建造）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsOpenUnion {
      get { return isOpenUnion_; }
      set {
        isOpenUnion_ = value;
      }
    }

    /// <summary>Field number for the "is_first_join_union" field.</summary>
    public const int IsFirstJoinUnionFieldNumber = 22;
    private bool isFirstJoinUnion_;
    /// <summary>
    /// 是否第一次加入联盟
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsFirstJoinUnion {
      get { return isFirstJoinUnion_; }
      set {
        isFirstJoinUnion_ = value;
      }
    }

    /// <summary>Field number for the "headquartersLevel" field.</summary>
    public const int HeadquartersLevelFieldNumber = 23;
    private uint headquartersLevel_;
    /// <summary>
    /// 总部等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint HeadquartersLevel {
      get { return headquartersLevel_; }
      set {
        headquartersLevel_ = value;
      }
    }

    /// <summary>Field number for the "union_join_time" field.</summary>
    public const int UnionJoinTimeFieldNumber = 24;
    private long unionJoinTime_;
    /// <summary>
    /// 加入联盟时间戳
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long UnionJoinTime {
      get { return unionJoinTime_; }
      set {
        unionJoinTime_ = value;
      }
    }

    /// <summary>Field number for the "kill_solder_num" field.</summary>
    public const int KillSolderNumFieldNumber = 25;
    private ulong killSolderNum_;
    /// <summary>
    /// 击败士兵数量的总和
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong KillSolderNum {
      get { return killSolderNum_; }
      set {
        killSolderNum_ = value;
      }
    }

    /// <summary>Field number for the "vip_level" field.</summary>
    public const int VipLevelFieldNumber = 26;
    private int vipLevel_;
    /// <summary>
    /// vip等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int VipLevel {
      get { return vipLevel_; }
      set {
        vipLevel_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RoleBrief);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RoleBrief other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ServerId != other.ServerId) return false;
      if (ServerName != other.ServerName) return false;
      if (Id != other.Id) return false;
      if (Name != other.Name) return false;
      if (Level != other.Level) return false;
      if (IsCustomAvatar != other.IsCustomAvatar) return false;
      if (HeadSystemAvatar != other.HeadSystemAvatar) return false;
      if (HeadCustomAvatar != other.HeadCustomAvatar) return false;
      if (HeadBorder != other.HeadBorder) return false;
      if (Power != other.Power) return false;
      if (LeagueName != other.LeagueName) return false;
      if (Lang != other.Lang) return false;
      if (Gender != other.Gender) return false;
      if (CreateAt != other.CreateAt) return false;
      if (LoginAt != other.LoginAt) return false;
      if (LogoutAt != other.LogoutAt) return false;
      if (UnionId != other.UnionId) return false;
      if (UnionPosition != other.UnionPosition) return false;
      if (UnionPermission != other.UnionPermission) return false;
      if (UnionLeaveTime != other.UnionLeaveTime) return false;
      if (IsOpenUnion != other.IsOpenUnion) return false;
      if (IsFirstJoinUnion != other.IsFirstJoinUnion) return false;
      if (HeadquartersLevel != other.HeadquartersLevel) return false;
      if (UnionJoinTime != other.UnionJoinTime) return false;
      if (KillSolderNum != other.KillSolderNum) return false;
      if (VipLevel != other.VipLevel) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ServerId != 0) hash ^= ServerId.GetHashCode();
      if (ServerName.Length != 0) hash ^= ServerName.GetHashCode();
      if (Id != 0UL) hash ^= Id.GetHashCode();
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (Level != 0) hash ^= Level.GetHashCode();
      if (IsCustomAvatar != false) hash ^= IsCustomAvatar.GetHashCode();
      if (HeadSystemAvatar != 0) hash ^= HeadSystemAvatar.GetHashCode();
      if (HeadCustomAvatar.Length != 0) hash ^= HeadCustomAvatar.GetHashCode();
      if (HeadBorder != 0) hash ^= HeadBorder.GetHashCode();
      if (Power != 0UL) hash ^= Power.GetHashCode();
      if (LeagueName.Length != 0) hash ^= LeagueName.GetHashCode();
      if (Lang != 0) hash ^= Lang.GetHashCode();
      if (Gender != 0) hash ^= Gender.GetHashCode();
      if (CreateAt != 0L) hash ^= CreateAt.GetHashCode();
      if (LoginAt != 0L) hash ^= LoginAt.GetHashCode();
      if (LogoutAt != 0L) hash ^= LogoutAt.GetHashCode();
      if (UnionId != 0UL) hash ^= UnionId.GetHashCode();
      if (UnionPosition != 0) hash ^= UnionPosition.GetHashCode();
      if (UnionPermission != 0) hash ^= UnionPermission.GetHashCode();
      if (UnionLeaveTime != 0L) hash ^= UnionLeaveTime.GetHashCode();
      if (IsOpenUnion != false) hash ^= IsOpenUnion.GetHashCode();
      if (IsFirstJoinUnion != false) hash ^= IsFirstJoinUnion.GetHashCode();
      if (HeadquartersLevel != 0) hash ^= HeadquartersLevel.GetHashCode();
      if (UnionJoinTime != 0L) hash ^= UnionJoinTime.GetHashCode();
      if (KillSolderNum != 0UL) hash ^= KillSolderNum.GetHashCode();
      if (VipLevel != 0) hash ^= VipLevel.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ServerId != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(ServerId);
      }
      if (ServerName.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ServerName);
      }
      if (Id != 0UL) {
        output.WriteRawTag(24);
        output.WriteUInt64(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Name);
      }
      if (Level != 0) {
        output.WriteRawTag(40);
        output.WriteUInt32(Level);
      }
      if (IsCustomAvatar != false) {
        output.WriteRawTag(48);
        output.WriteBool(IsCustomAvatar);
      }
      if (HeadSystemAvatar != 0) {
        output.WriteRawTag(56);
        output.WriteUInt32(HeadSystemAvatar);
      }
      if (HeadCustomAvatar.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(HeadCustomAvatar);
      }
      if (HeadBorder != 0) {
        output.WriteRawTag(72);
        output.WriteUInt32(HeadBorder);
      }
      if (Power != 0UL) {
        output.WriteRawTag(80);
        output.WriteUInt64(Power);
      }
      if (LeagueName.Length != 0) {
        output.WriteRawTag(90);
        output.WriteString(LeagueName);
      }
      if (Lang != 0) {
        output.WriteRawTag(96);
        output.WriteInt32(Lang);
      }
      if (Gender != 0) {
        output.WriteRawTag(104);
        output.WriteInt32(Gender);
      }
      if (CreateAt != 0L) {
        output.WriteRawTag(112);
        output.WriteInt64(CreateAt);
      }
      if (LoginAt != 0L) {
        output.WriteRawTag(120);
        output.WriteInt64(LoginAt);
      }
      if (LogoutAt != 0L) {
        output.WriteRawTag(128, 1);
        output.WriteInt64(LogoutAt);
      }
      if (UnionId != 0UL) {
        output.WriteRawTag(136, 1);
        output.WriteUInt64(UnionId);
      }
      if (UnionPosition != 0) {
        output.WriteRawTag(144, 1);
        output.WriteInt32(UnionPosition);
      }
      if (UnionPermission != 0) {
        output.WriteRawTag(152, 1);
        output.WriteInt32(UnionPermission);
      }
      if (UnionLeaveTime != 0L) {
        output.WriteRawTag(160, 1);
        output.WriteInt64(UnionLeaveTime);
      }
      if (IsOpenUnion != false) {
        output.WriteRawTag(168, 1);
        output.WriteBool(IsOpenUnion);
      }
      if (IsFirstJoinUnion != false) {
        output.WriteRawTag(176, 1);
        output.WriteBool(IsFirstJoinUnion);
      }
      if (HeadquartersLevel != 0) {
        output.WriteRawTag(184, 1);
        output.WriteUInt32(HeadquartersLevel);
      }
      if (UnionJoinTime != 0L) {
        output.WriteRawTag(192, 1);
        output.WriteInt64(UnionJoinTime);
      }
      if (KillSolderNum != 0UL) {
        output.WriteRawTag(200, 1);
        output.WriteUInt64(KillSolderNum);
      }
      if (VipLevel != 0) {
        output.WriteRawTag(208, 1);
        output.WriteInt32(VipLevel);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ServerId != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(ServerId);
      }
      if (ServerName.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ServerName);
      }
      if (Id != 0UL) {
        output.WriteRawTag(24);
        output.WriteUInt64(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Name);
      }
      if (Level != 0) {
        output.WriteRawTag(40);
        output.WriteUInt32(Level);
      }
      if (IsCustomAvatar != false) {
        output.WriteRawTag(48);
        output.WriteBool(IsCustomAvatar);
      }
      if (HeadSystemAvatar != 0) {
        output.WriteRawTag(56);
        output.WriteUInt32(HeadSystemAvatar);
      }
      if (HeadCustomAvatar.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(HeadCustomAvatar);
      }
      if (HeadBorder != 0) {
        output.WriteRawTag(72);
        output.WriteUInt32(HeadBorder);
      }
      if (Power != 0UL) {
        output.WriteRawTag(80);
        output.WriteUInt64(Power);
      }
      if (LeagueName.Length != 0) {
        output.WriteRawTag(90);
        output.WriteString(LeagueName);
      }
      if (Lang != 0) {
        output.WriteRawTag(96);
        output.WriteInt32(Lang);
      }
      if (Gender != 0) {
        output.WriteRawTag(104);
        output.WriteInt32(Gender);
      }
      if (CreateAt != 0L) {
        output.WriteRawTag(112);
        output.WriteInt64(CreateAt);
      }
      if (LoginAt != 0L) {
        output.WriteRawTag(120);
        output.WriteInt64(LoginAt);
      }
      if (LogoutAt != 0L) {
        output.WriteRawTag(128, 1);
        output.WriteInt64(LogoutAt);
      }
      if (UnionId != 0UL) {
        output.WriteRawTag(136, 1);
        output.WriteUInt64(UnionId);
      }
      if (UnionPosition != 0) {
        output.WriteRawTag(144, 1);
        output.WriteInt32(UnionPosition);
      }
      if (UnionPermission != 0) {
        output.WriteRawTag(152, 1);
        output.WriteInt32(UnionPermission);
      }
      if (UnionLeaveTime != 0L) {
        output.WriteRawTag(160, 1);
        output.WriteInt64(UnionLeaveTime);
      }
      if (IsOpenUnion != false) {
        output.WriteRawTag(168, 1);
        output.WriteBool(IsOpenUnion);
      }
      if (IsFirstJoinUnion != false) {
        output.WriteRawTag(176, 1);
        output.WriteBool(IsFirstJoinUnion);
      }
      if (HeadquartersLevel != 0) {
        output.WriteRawTag(184, 1);
        output.WriteUInt32(HeadquartersLevel);
      }
      if (UnionJoinTime != 0L) {
        output.WriteRawTag(192, 1);
        output.WriteInt64(UnionJoinTime);
      }
      if (KillSolderNum != 0UL) {
        output.WriteRawTag(200, 1);
        output.WriteUInt64(KillSolderNum);
      }
      if (VipLevel != 0) {
        output.WriteRawTag(208, 1);
        output.WriteInt32(VipLevel);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ServerId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(ServerId);
      }
      if (ServerName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ServerName);
      }
      if (Id != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(Id);
      }
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (Level != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Level);
      }
      if (IsCustomAvatar != false) {
        size += 1 + 1;
      }
      if (HeadSystemAvatar != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(HeadSystemAvatar);
      }
      if (HeadCustomAvatar.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(HeadCustomAvatar);
      }
      if (HeadBorder != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(HeadBorder);
      }
      if (Power != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(Power);
      }
      if (LeagueName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(LeagueName);
      }
      if (Lang != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Lang);
      }
      if (Gender != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Gender);
      }
      if (CreateAt != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(CreateAt);
      }
      if (LoginAt != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(LoginAt);
      }
      if (LogoutAt != 0L) {
        size += 2 + pb::CodedOutputStream.ComputeInt64Size(LogoutAt);
      }
      if (UnionId != 0UL) {
        size += 2 + pb::CodedOutputStream.ComputeUInt64Size(UnionId);
      }
      if (UnionPosition != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(UnionPosition);
      }
      if (UnionPermission != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(UnionPermission);
      }
      if (UnionLeaveTime != 0L) {
        size += 2 + pb::CodedOutputStream.ComputeInt64Size(UnionLeaveTime);
      }
      if (IsOpenUnion != false) {
        size += 2 + 1;
      }
      if (IsFirstJoinUnion != false) {
        size += 2 + 1;
      }
      if (HeadquartersLevel != 0) {
        size += 2 + pb::CodedOutputStream.ComputeUInt32Size(HeadquartersLevel);
      }
      if (UnionJoinTime != 0L) {
        size += 2 + pb::CodedOutputStream.ComputeInt64Size(UnionJoinTime);
      }
      if (KillSolderNum != 0UL) {
        size += 2 + pb::CodedOutputStream.ComputeUInt64Size(KillSolderNum);
      }
      if (VipLevel != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(VipLevel);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RoleBrief other) {
      if (other == null) {
        return;
      }
      if (other.ServerId != 0) {
        ServerId = other.ServerId;
      }
      if (other.ServerName.Length != 0) {
        ServerName = other.ServerName;
      }
      if (other.Id != 0UL) {
        Id = other.Id;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.Level != 0) {
        Level = other.Level;
      }
      if (other.IsCustomAvatar != false) {
        IsCustomAvatar = other.IsCustomAvatar;
      }
      if (other.HeadSystemAvatar != 0) {
        HeadSystemAvatar = other.HeadSystemAvatar;
      }
      if (other.HeadCustomAvatar.Length != 0) {
        HeadCustomAvatar = other.HeadCustomAvatar;
      }
      if (other.HeadBorder != 0) {
        HeadBorder = other.HeadBorder;
      }
      if (other.Power != 0UL) {
        Power = other.Power;
      }
      if (other.LeagueName.Length != 0) {
        LeagueName = other.LeagueName;
      }
      if (other.Lang != 0) {
        Lang = other.Lang;
      }
      if (other.Gender != 0) {
        Gender = other.Gender;
      }
      if (other.CreateAt != 0L) {
        CreateAt = other.CreateAt;
      }
      if (other.LoginAt != 0L) {
        LoginAt = other.LoginAt;
      }
      if (other.LogoutAt != 0L) {
        LogoutAt = other.LogoutAt;
      }
      if (other.UnionId != 0UL) {
        UnionId = other.UnionId;
      }
      if (other.UnionPosition != 0) {
        UnionPosition = other.UnionPosition;
      }
      if (other.UnionPermission != 0) {
        UnionPermission = other.UnionPermission;
      }
      if (other.UnionLeaveTime != 0L) {
        UnionLeaveTime = other.UnionLeaveTime;
      }
      if (other.IsOpenUnion != false) {
        IsOpenUnion = other.IsOpenUnion;
      }
      if (other.IsFirstJoinUnion != false) {
        IsFirstJoinUnion = other.IsFirstJoinUnion;
      }
      if (other.HeadquartersLevel != 0) {
        HeadquartersLevel = other.HeadquartersLevel;
      }
      if (other.UnionJoinTime != 0L) {
        UnionJoinTime = other.UnionJoinTime;
      }
      if (other.KillSolderNum != 0UL) {
        KillSolderNum = other.KillSolderNum;
      }
      if (other.VipLevel != 0) {
        VipLevel = other.VipLevel;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            ServerId = input.ReadUInt32();
            break;
          }
          case 18: {
            ServerName = input.ReadString();
            break;
          }
          case 24: {
            Id = input.ReadUInt64();
            break;
          }
          case 34: {
            Name = input.ReadString();
            break;
          }
          case 40: {
            Level = input.ReadUInt32();
            break;
          }
          case 48: {
            IsCustomAvatar = input.ReadBool();
            break;
          }
          case 56: {
            HeadSystemAvatar = input.ReadUInt32();
            break;
          }
          case 66: {
            HeadCustomAvatar = input.ReadString();
            break;
          }
          case 72: {
            HeadBorder = input.ReadUInt32();
            break;
          }
          case 80: {
            Power = input.ReadUInt64();
            break;
          }
          case 90: {
            LeagueName = input.ReadString();
            break;
          }
          case 96: {
            Lang = input.ReadInt32();
            break;
          }
          case 104: {
            Gender = input.ReadInt32();
            break;
          }
          case 112: {
            CreateAt = input.ReadInt64();
            break;
          }
          case 120: {
            LoginAt = input.ReadInt64();
            break;
          }
          case 128: {
            LogoutAt = input.ReadInt64();
            break;
          }
          case 136: {
            UnionId = input.ReadUInt64();
            break;
          }
          case 144: {
            UnionPosition = input.ReadInt32();
            break;
          }
          case 152: {
            UnionPermission = input.ReadInt32();
            break;
          }
          case 160: {
            UnionLeaveTime = input.ReadInt64();
            break;
          }
          case 168: {
            IsOpenUnion = input.ReadBool();
            break;
          }
          case 176: {
            IsFirstJoinUnion = input.ReadBool();
            break;
          }
          case 184: {
            HeadquartersLevel = input.ReadUInt32();
            break;
          }
          case 192: {
            UnionJoinTime = input.ReadInt64();
            break;
          }
          case 200: {
            KillSolderNum = input.ReadUInt64();
            break;
          }
          case 208: {
            VipLevel = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            ServerId = input.ReadUInt32();
            break;
          }
          case 18: {
            ServerName = input.ReadString();
            break;
          }
          case 24: {
            Id = input.ReadUInt64();
            break;
          }
          case 34: {
            Name = input.ReadString();
            break;
          }
          case 40: {
            Level = input.ReadUInt32();
            break;
          }
          case 48: {
            IsCustomAvatar = input.ReadBool();
            break;
          }
          case 56: {
            HeadSystemAvatar = input.ReadUInt32();
            break;
          }
          case 66: {
            HeadCustomAvatar = input.ReadString();
            break;
          }
          case 72: {
            HeadBorder = input.ReadUInt32();
            break;
          }
          case 80: {
            Power = input.ReadUInt64();
            break;
          }
          case 90: {
            LeagueName = input.ReadString();
            break;
          }
          case 96: {
            Lang = input.ReadInt32();
            break;
          }
          case 104: {
            Gender = input.ReadInt32();
            break;
          }
          case 112: {
            CreateAt = input.ReadInt64();
            break;
          }
          case 120: {
            LoginAt = input.ReadInt64();
            break;
          }
          case 128: {
            LogoutAt = input.ReadInt64();
            break;
          }
          case 136: {
            UnionId = input.ReadUInt64();
            break;
          }
          case 144: {
            UnionPosition = input.ReadInt32();
            break;
          }
          case 152: {
            UnionPermission = input.ReadInt32();
            break;
          }
          case 160: {
            UnionLeaveTime = input.ReadInt64();
            break;
          }
          case 168: {
            IsOpenUnion = input.ReadBool();
            break;
          }
          case 176: {
            IsFirstJoinUnion = input.ReadBool();
            break;
          }
          case 184: {
            HeadquartersLevel = input.ReadUInt32();
            break;
          }
          case 192: {
            UnionJoinTime = input.ReadInt64();
            break;
          }
          case 200: {
            KillSolderNum = input.ReadUInt64();
            break;
          }
          case 208: {
            VipLevel = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// RoleInfo 角色完整数据，玩家登录时使用
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RoleInfo : pb::IMessage<RoleInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RoleInfo> _parser = new pb::MessageParser<RoleInfo>(() => new RoleInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RoleInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Roledata.RoledataReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleInfo(RoleInfo other) : this() {
      id_ = other.id_;
      attr_ = other.attr_ != null ? other.attr_.Clone() : null;
      build_ = other.build_ != null ? other.build_.Clone() : null;
      union_ = other.union_ != null ? other.union_.Clone() : null;
      item_ = other.item_ != null ? other.item_.Clone() : null;
      hero_ = other.hero_ != null ? other.hero_.Clone() : null;
      equip_ = other.equip_ != null ? other.equip_.Clone() : null;
      queue_ = other.queue_ != null ? other.queue_.Clone() : null;
      workers_ = other.workers_ != null ? other.workers_.Clone() : null;
      soldiers_ = other.soldiers_ != null ? other.soldiers_.Clone() : null;
      hospitals_ = other.hospitals_ != null ? other.hospitals_.Clone() : null;
      survivors_ = other.survivors_ != null ? other.survivors_.Clone() : null;
      uav_ = other.uav_ != null ? other.uav_.Clone() : null;
      techs_ = other.techs_ != null ? other.techs_.Clone() : null;
      teams_ = other.teams_ != null ? other.teams_.Clone() : null;
      dungeons_ = other.dungeons_ != null ? other.dungeons_.Clone() : null;
      tasks_ = other.tasks_ != null ? other.tasks_.Clone() : null;
      trade_ = other.trade_ != null ? other.trade_.Clone() : null;
      privileges_ = other.privileges_ != null ? other.privileges_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleInfo Clone() {
      return new RoleInfo(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private ulong id_;
    /// <summary>
    /// 角色 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "attr" field.</summary>
    public const int AttrFieldNumber = 2;
    private global::Roledata.RoleAttr attr_;
    /// <summary>
    /// 角色属性
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Roledata.RoleAttr Attr {
      get { return attr_; }
      set {
        attr_ = value;
      }
    }

    /// <summary>Field number for the "build" field.</summary>
    public const int BuildFieldNumber = 3;
    private global::Roledata.RoleBuild build_;
    /// <summary>
    /// 角色建筑
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Roledata.RoleBuild Build {
      get { return build_; }
      set {
        build_ = value;
      }
    }

    /// <summary>Field number for the "union" field.</summary>
    public const int UnionFieldNumber = 4;
    private global::Roledata.RoleUnion union_;
    /// <summary>
    /// 联盟
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Roledata.RoleUnion Union {
      get { return union_; }
      set {
        union_ = value;
      }
    }

    /// <summary>Field number for the "item" field.</summary>
    public const int ItemFieldNumber = 5;
    private global::Roledata.RoleItem item_;
    /// <summary>
    /// 道具
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Roledata.RoleItem Item {
      get { return item_; }
      set {
        item_ = value;
      }
    }

    /// <summary>Field number for the "hero" field.</summary>
    public const int HeroFieldNumber = 6;
    private global::Roledata.RoleHero hero_;
    /// <summary>
    /// 英雄
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Roledata.RoleHero Hero {
      get { return hero_; }
      set {
        hero_ = value;
      }
    }

    /// <summary>Field number for the "equip" field.</summary>
    public const int EquipFieldNumber = 7;
    private global::Roledata.RoleEquip equip_;
    /// <summary>
    /// 装备
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Roledata.RoleEquip Equip {
      get { return equip_; }
      set {
        equip_ = value;
      }
    }

    /// <summary>Field number for the "queue" field.</summary>
    public const int QueueFieldNumber = 8;
    private global::Roledata.RoleQueue queue_;
    /// <summary>
    /// 排队信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Roledata.RoleQueue Queue {
      get { return queue_; }
      set {
        queue_ = value;
      }
    }

    /// <summary>Field number for the "workers" field.</summary>
    public const int WorkersFieldNumber = 9;
    private global::Roledata.RoleBuildWorker workers_;
    /// <summary>
    /// 建筑工人信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Roledata.RoleBuildWorker Workers {
      get { return workers_; }
      set {
        workers_ = value;
      }
    }

    /// <summary>Field number for the "soldiers" field.</summary>
    public const int SoldiersFieldNumber = 10;
    private global::Roledata.RoleSoldier soldiers_;
    /// <summary>
    /// 兵营信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Roledata.RoleSoldier Soldiers {
      get { return soldiers_; }
      set {
        soldiers_ = value;
      }
    }

    /// <summary>Field number for the "hospitals" field.</summary>
    public const int HospitalsFieldNumber = 11;
    private global::Roledata.RoleSoldier hospitals_;
    /// <summary>
    /// 医院信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Roledata.RoleSoldier Hospitals {
      get { return hospitals_; }
      set {
        hospitals_ = value;
      }
    }

    /// <summary>Field number for the "survivors" field.</summary>
    public const int SurvivorsFieldNumber = 12;
    private global::Roledata.RoleSurvivor survivors_;
    /// <summary>
    /// 幸存者信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Roledata.RoleSurvivor Survivors {
      get { return survivors_; }
      set {
        survivors_ = value;
      }
    }

    /// <summary>Field number for the "uav" field.</summary>
    public const int UavFieldNumber = 13;
    private global::Uav.UAV uav_;
    /// <summary>
    /// TODO 建筑占位 13 - 20
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Uav.UAV Uav {
      get { return uav_; }
      set {
        uav_ = value;
      }
    }

    /// <summary>Field number for the "techs" field.</summary>
    public const int TechsFieldNumber = 14;
    private global::Roledata.RoleTech techs_;
    /// <summary>
    /// 科技信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Roledata.RoleTech Techs {
      get { return techs_; }
      set {
        techs_ = value;
      }
    }

    /// <summary>Field number for the "teams" field.</summary>
    public const int TeamsFieldNumber = 21;
    private global::Roledata.RoleTeam teams_;
    /// <summary>
    /// 队伍信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Roledata.RoleTeam Teams {
      get { return teams_; }
      set {
        teams_ = value;
      }
    }

    /// <summary>Field number for the "dungeons" field.</summary>
    public const int DungeonsFieldNumber = 22;
    private global::Roledata.RoleDungeon dungeons_;
    /// <summary>
    /// 关卡信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Roledata.RoleDungeon Dungeons {
      get { return dungeons_; }
      set {
        dungeons_ = value;
      }
    }

    /// <summary>Field number for the "tasks" field.</summary>
    public const int TasksFieldNumber = 23;
    private global::Roledata.RoleTask tasks_;
    /// <summary>
    /// 任务信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Roledata.RoleTask Tasks {
      get { return tasks_; }
      set {
        tasks_ = value;
      }
    }

    /// <summary>Field number for the "trade" field.</summary>
    public const int TradeFieldNumber = 24;
    private global::Roledata.RoleTrade trade_;
    /// <summary>
    /// 城际贸易
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Roledata.RoleTrade Trade {
      get { return trade_; }
      set {
        trade_ = value;
      }
    }

    /// <summary>Field number for the "privileges" field.</summary>
    public const int PrivilegesFieldNumber = 25;
    private global::Roledata.RolePrivilege privileges_;
    /// <summary>
    /// 特权信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Roledata.RolePrivilege Privileges {
      get { return privileges_; }
      set {
        privileges_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RoleInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RoleInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (!object.Equals(Attr, other.Attr)) return false;
      if (!object.Equals(Build, other.Build)) return false;
      if (!object.Equals(Union, other.Union)) return false;
      if (!object.Equals(Item, other.Item)) return false;
      if (!object.Equals(Hero, other.Hero)) return false;
      if (!object.Equals(Equip, other.Equip)) return false;
      if (!object.Equals(Queue, other.Queue)) return false;
      if (!object.Equals(Workers, other.Workers)) return false;
      if (!object.Equals(Soldiers, other.Soldiers)) return false;
      if (!object.Equals(Hospitals, other.Hospitals)) return false;
      if (!object.Equals(Survivors, other.Survivors)) return false;
      if (!object.Equals(Uav, other.Uav)) return false;
      if (!object.Equals(Techs, other.Techs)) return false;
      if (!object.Equals(Teams, other.Teams)) return false;
      if (!object.Equals(Dungeons, other.Dungeons)) return false;
      if (!object.Equals(Tasks, other.Tasks)) return false;
      if (!object.Equals(Trade, other.Trade)) return false;
      if (!object.Equals(Privileges, other.Privileges)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0UL) hash ^= Id.GetHashCode();
      if (attr_ != null) hash ^= Attr.GetHashCode();
      if (build_ != null) hash ^= Build.GetHashCode();
      if (union_ != null) hash ^= Union.GetHashCode();
      if (item_ != null) hash ^= Item.GetHashCode();
      if (hero_ != null) hash ^= Hero.GetHashCode();
      if (equip_ != null) hash ^= Equip.GetHashCode();
      if (queue_ != null) hash ^= Queue.GetHashCode();
      if (workers_ != null) hash ^= Workers.GetHashCode();
      if (soldiers_ != null) hash ^= Soldiers.GetHashCode();
      if (hospitals_ != null) hash ^= Hospitals.GetHashCode();
      if (survivors_ != null) hash ^= Survivors.GetHashCode();
      if (uav_ != null) hash ^= Uav.GetHashCode();
      if (techs_ != null) hash ^= Techs.GetHashCode();
      if (teams_ != null) hash ^= Teams.GetHashCode();
      if (dungeons_ != null) hash ^= Dungeons.GetHashCode();
      if (tasks_ != null) hash ^= Tasks.GetHashCode();
      if (trade_ != null) hash ^= Trade.GetHashCode();
      if (privileges_ != null) hash ^= Privileges.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(Id);
      }
      if (attr_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Attr);
      }
      if (build_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(Build);
      }
      if (union_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(Union);
      }
      if (item_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(Item);
      }
      if (hero_ != null) {
        output.WriteRawTag(50);
        output.WriteMessage(Hero);
      }
      if (equip_ != null) {
        output.WriteRawTag(58);
        output.WriteMessage(Equip);
      }
      if (queue_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(Queue);
      }
      if (workers_ != null) {
        output.WriteRawTag(74);
        output.WriteMessage(Workers);
      }
      if (soldiers_ != null) {
        output.WriteRawTag(82);
        output.WriteMessage(Soldiers);
      }
      if (hospitals_ != null) {
        output.WriteRawTag(90);
        output.WriteMessage(Hospitals);
      }
      if (survivors_ != null) {
        output.WriteRawTag(98);
        output.WriteMessage(Survivors);
      }
      if (uav_ != null) {
        output.WriteRawTag(106);
        output.WriteMessage(Uav);
      }
      if (techs_ != null) {
        output.WriteRawTag(114);
        output.WriteMessage(Techs);
      }
      if (teams_ != null) {
        output.WriteRawTag(170, 1);
        output.WriteMessage(Teams);
      }
      if (dungeons_ != null) {
        output.WriteRawTag(178, 1);
        output.WriteMessage(Dungeons);
      }
      if (tasks_ != null) {
        output.WriteRawTag(186, 1);
        output.WriteMessage(Tasks);
      }
      if (trade_ != null) {
        output.WriteRawTag(194, 1);
        output.WriteMessage(Trade);
      }
      if (privileges_ != null) {
        output.WriteRawTag(202, 1);
        output.WriteMessage(Privileges);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(Id);
      }
      if (attr_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Attr);
      }
      if (build_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(Build);
      }
      if (union_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(Union);
      }
      if (item_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(Item);
      }
      if (hero_ != null) {
        output.WriteRawTag(50);
        output.WriteMessage(Hero);
      }
      if (equip_ != null) {
        output.WriteRawTag(58);
        output.WriteMessage(Equip);
      }
      if (queue_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(Queue);
      }
      if (workers_ != null) {
        output.WriteRawTag(74);
        output.WriteMessage(Workers);
      }
      if (soldiers_ != null) {
        output.WriteRawTag(82);
        output.WriteMessage(Soldiers);
      }
      if (hospitals_ != null) {
        output.WriteRawTag(90);
        output.WriteMessage(Hospitals);
      }
      if (survivors_ != null) {
        output.WriteRawTag(98);
        output.WriteMessage(Survivors);
      }
      if (uav_ != null) {
        output.WriteRawTag(106);
        output.WriteMessage(Uav);
      }
      if (techs_ != null) {
        output.WriteRawTag(114);
        output.WriteMessage(Techs);
      }
      if (teams_ != null) {
        output.WriteRawTag(170, 1);
        output.WriteMessage(Teams);
      }
      if (dungeons_ != null) {
        output.WriteRawTag(178, 1);
        output.WriteMessage(Dungeons);
      }
      if (tasks_ != null) {
        output.WriteRawTag(186, 1);
        output.WriteMessage(Tasks);
      }
      if (trade_ != null) {
        output.WriteRawTag(194, 1);
        output.WriteMessage(Trade);
      }
      if (privileges_ != null) {
        output.WriteRawTag(202, 1);
        output.WriteMessage(Privileges);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(Id);
      }
      if (attr_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Attr);
      }
      if (build_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Build);
      }
      if (union_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Union);
      }
      if (item_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Item);
      }
      if (hero_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Hero);
      }
      if (equip_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Equip);
      }
      if (queue_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Queue);
      }
      if (workers_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Workers);
      }
      if (soldiers_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Soldiers);
      }
      if (hospitals_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Hospitals);
      }
      if (survivors_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Survivors);
      }
      if (uav_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Uav);
      }
      if (techs_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Techs);
      }
      if (teams_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(Teams);
      }
      if (dungeons_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(Dungeons);
      }
      if (tasks_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(Tasks);
      }
      if (trade_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(Trade);
      }
      if (privileges_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(Privileges);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RoleInfo other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0UL) {
        Id = other.Id;
      }
      if (other.attr_ != null) {
        if (attr_ == null) {
          Attr = new global::Roledata.RoleAttr();
        }
        Attr.MergeFrom(other.Attr);
      }
      if (other.build_ != null) {
        if (build_ == null) {
          Build = new global::Roledata.RoleBuild();
        }
        Build.MergeFrom(other.Build);
      }
      if (other.union_ != null) {
        if (union_ == null) {
          Union = new global::Roledata.RoleUnion();
        }
        Union.MergeFrom(other.Union);
      }
      if (other.item_ != null) {
        if (item_ == null) {
          Item = new global::Roledata.RoleItem();
        }
        Item.MergeFrom(other.Item);
      }
      if (other.hero_ != null) {
        if (hero_ == null) {
          Hero = new global::Roledata.RoleHero();
        }
        Hero.MergeFrom(other.Hero);
      }
      if (other.equip_ != null) {
        if (equip_ == null) {
          Equip = new global::Roledata.RoleEquip();
        }
        Equip.MergeFrom(other.Equip);
      }
      if (other.queue_ != null) {
        if (queue_ == null) {
          Queue = new global::Roledata.RoleQueue();
        }
        Queue.MergeFrom(other.Queue);
      }
      if (other.workers_ != null) {
        if (workers_ == null) {
          Workers = new global::Roledata.RoleBuildWorker();
        }
        Workers.MergeFrom(other.Workers);
      }
      if (other.soldiers_ != null) {
        if (soldiers_ == null) {
          Soldiers = new global::Roledata.RoleSoldier();
        }
        Soldiers.MergeFrom(other.Soldiers);
      }
      if (other.hospitals_ != null) {
        if (hospitals_ == null) {
          Hospitals = new global::Roledata.RoleSoldier();
        }
        Hospitals.MergeFrom(other.Hospitals);
      }
      if (other.survivors_ != null) {
        if (survivors_ == null) {
          Survivors = new global::Roledata.RoleSurvivor();
        }
        Survivors.MergeFrom(other.Survivors);
      }
      if (other.uav_ != null) {
        if (uav_ == null) {
          Uav = new global::Uav.UAV();
        }
        Uav.MergeFrom(other.Uav);
      }
      if (other.techs_ != null) {
        if (techs_ == null) {
          Techs = new global::Roledata.RoleTech();
        }
        Techs.MergeFrom(other.Techs);
      }
      if (other.teams_ != null) {
        if (teams_ == null) {
          Teams = new global::Roledata.RoleTeam();
        }
        Teams.MergeFrom(other.Teams);
      }
      if (other.dungeons_ != null) {
        if (dungeons_ == null) {
          Dungeons = new global::Roledata.RoleDungeon();
        }
        Dungeons.MergeFrom(other.Dungeons);
      }
      if (other.tasks_ != null) {
        if (tasks_ == null) {
          Tasks = new global::Roledata.RoleTask();
        }
        Tasks.MergeFrom(other.Tasks);
      }
      if (other.trade_ != null) {
        if (trade_ == null) {
          Trade = new global::Roledata.RoleTrade();
        }
        Trade.MergeFrom(other.Trade);
      }
      if (other.privileges_ != null) {
        if (privileges_ == null) {
          Privileges = new global::Roledata.RolePrivilege();
        }
        Privileges.MergeFrom(other.Privileges);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadUInt64();
            break;
          }
          case 18: {
            if (attr_ == null) {
              Attr = new global::Roledata.RoleAttr();
            }
            input.ReadMessage(Attr);
            break;
          }
          case 26: {
            if (build_ == null) {
              Build = new global::Roledata.RoleBuild();
            }
            input.ReadMessage(Build);
            break;
          }
          case 34: {
            if (union_ == null) {
              Union = new global::Roledata.RoleUnion();
            }
            input.ReadMessage(Union);
            break;
          }
          case 42: {
            if (item_ == null) {
              Item = new global::Roledata.RoleItem();
            }
            input.ReadMessage(Item);
            break;
          }
          case 50: {
            if (hero_ == null) {
              Hero = new global::Roledata.RoleHero();
            }
            input.ReadMessage(Hero);
            break;
          }
          case 58: {
            if (equip_ == null) {
              Equip = new global::Roledata.RoleEquip();
            }
            input.ReadMessage(Equip);
            break;
          }
          case 66: {
            if (queue_ == null) {
              Queue = new global::Roledata.RoleQueue();
            }
            input.ReadMessage(Queue);
            break;
          }
          case 74: {
            if (workers_ == null) {
              Workers = new global::Roledata.RoleBuildWorker();
            }
            input.ReadMessage(Workers);
            break;
          }
          case 82: {
            if (soldiers_ == null) {
              Soldiers = new global::Roledata.RoleSoldier();
            }
            input.ReadMessage(Soldiers);
            break;
          }
          case 90: {
            if (hospitals_ == null) {
              Hospitals = new global::Roledata.RoleSoldier();
            }
            input.ReadMessage(Hospitals);
            break;
          }
          case 98: {
            if (survivors_ == null) {
              Survivors = new global::Roledata.RoleSurvivor();
            }
            input.ReadMessage(Survivors);
            break;
          }
          case 106: {
            if (uav_ == null) {
              Uav = new global::Uav.UAV();
            }
            input.ReadMessage(Uav);
            break;
          }
          case 114: {
            if (techs_ == null) {
              Techs = new global::Roledata.RoleTech();
            }
            input.ReadMessage(Techs);
            break;
          }
          case 170: {
            if (teams_ == null) {
              Teams = new global::Roledata.RoleTeam();
            }
            input.ReadMessage(Teams);
            break;
          }
          case 178: {
            if (dungeons_ == null) {
              Dungeons = new global::Roledata.RoleDungeon();
            }
            input.ReadMessage(Dungeons);
            break;
          }
          case 186: {
            if (tasks_ == null) {
              Tasks = new global::Roledata.RoleTask();
            }
            input.ReadMessage(Tasks);
            break;
          }
          case 194: {
            if (trade_ == null) {
              Trade = new global::Roledata.RoleTrade();
            }
            input.ReadMessage(Trade);
            break;
          }
          case 202: {
            if (privileges_ == null) {
              Privileges = new global::Roledata.RolePrivilege();
            }
            input.ReadMessage(Privileges);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadUInt64();
            break;
          }
          case 18: {
            if (attr_ == null) {
              Attr = new global::Roledata.RoleAttr();
            }
            input.ReadMessage(Attr);
            break;
          }
          case 26: {
            if (build_ == null) {
              Build = new global::Roledata.RoleBuild();
            }
            input.ReadMessage(Build);
            break;
          }
          case 34: {
            if (union_ == null) {
              Union = new global::Roledata.RoleUnion();
            }
            input.ReadMessage(Union);
            break;
          }
          case 42: {
            if (item_ == null) {
              Item = new global::Roledata.RoleItem();
            }
            input.ReadMessage(Item);
            break;
          }
          case 50: {
            if (hero_ == null) {
              Hero = new global::Roledata.RoleHero();
            }
            input.ReadMessage(Hero);
            break;
          }
          case 58: {
            if (equip_ == null) {
              Equip = new global::Roledata.RoleEquip();
            }
            input.ReadMessage(Equip);
            break;
          }
          case 66: {
            if (queue_ == null) {
              Queue = new global::Roledata.RoleQueue();
            }
            input.ReadMessage(Queue);
            break;
          }
          case 74: {
            if (workers_ == null) {
              Workers = new global::Roledata.RoleBuildWorker();
            }
            input.ReadMessage(Workers);
            break;
          }
          case 82: {
            if (soldiers_ == null) {
              Soldiers = new global::Roledata.RoleSoldier();
            }
            input.ReadMessage(Soldiers);
            break;
          }
          case 90: {
            if (hospitals_ == null) {
              Hospitals = new global::Roledata.RoleSoldier();
            }
            input.ReadMessage(Hospitals);
            break;
          }
          case 98: {
            if (survivors_ == null) {
              Survivors = new global::Roledata.RoleSurvivor();
            }
            input.ReadMessage(Survivors);
            break;
          }
          case 106: {
            if (uav_ == null) {
              Uav = new global::Uav.UAV();
            }
            input.ReadMessage(Uav);
            break;
          }
          case 114: {
            if (techs_ == null) {
              Techs = new global::Roledata.RoleTech();
            }
            input.ReadMessage(Techs);
            break;
          }
          case 170: {
            if (teams_ == null) {
              Teams = new global::Roledata.RoleTeam();
            }
            input.ReadMessage(Teams);
            break;
          }
          case 178: {
            if (dungeons_ == null) {
              Dungeons = new global::Roledata.RoleDungeon();
            }
            input.ReadMessage(Dungeons);
            break;
          }
          case 186: {
            if (tasks_ == null) {
              Tasks = new global::Roledata.RoleTask();
            }
            input.ReadMessage(Tasks);
            break;
          }
          case 194: {
            if (trade_ == null) {
              Trade = new global::Roledata.RoleTrade();
            }
            input.ReadMessage(Trade);
            break;
          }
          case 202: {
            if (privileges_ == null) {
              Privileges = new global::Roledata.RolePrivilege();
            }
            input.ReadMessage(Privileges);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// RoleAttr 角色属性
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RoleAttr : pb::IMessage<RoleAttr>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RoleAttr> _parser = new pb::MessageParser<RoleAttr>(() => new RoleAttr());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RoleAttr> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Roledata.RoledataReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleAttr() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleAttr(RoleAttr other) : this() {
      serverId_ = other.serverId_;
      serverName_ = other.serverName_;
      name_ = other.name_;
      isCustomAvatar_ = other.isCustomAvatar_;
      headSystemAvatar_ = other.headSystemAvatar_;
      headCustomAvatar_ = other.headCustomAvatar_;
      headBorder_ = other.headBorder_;
      power_ = other.power_;
      lastLoginAt_ = other.lastLoginAt_;
      lastLogoutAt_ = other.lastLogoutAt_;
      createAt_ = other.createAt_;
      lang_ = other.lang_;
      gender_ = other.gender_;
      killSolderNum_ = other.killSolderNum_;
      innerCityGridId_ = other.innerCityGridId_;
      innerCityRegionId_ = other.innerCityRegionId_;
      vipLevel_ = other.vipLevel_;
      vipExp_ = other.vipExp_;
      vipEndTime_ = other.vipEndTime_;
      isVipDailyPointReceive_ = other.isVipDailyPointReceive_;
      isVipDailyGiftReceive_ = other.isVipDailyGiftReceive_;
      vipTodayBuyPoints_ = other.vipTodayBuyPoints_;
      loginContinueDays_ = other.loginContinueDays_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleAttr Clone() {
      return new RoleAttr(this);
    }

    /// <summary>Field number for the "server_id" field.</summary>
    public const int ServerIdFieldNumber = 1;
    private uint serverId_;
    /// <summary>
    /// 所在区服 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint ServerId {
      get { return serverId_; }
      set {
        serverId_ = value;
      }
    }

    /// <summary>Field number for the "server_name" field.</summary>
    public const int ServerNameFieldNumber = 2;
    private string serverName_ = "";
    /// <summary>
    /// 所在区服名称
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ServerName {
      get { return serverName_; }
      set {
        serverName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int NameFieldNumber = 3;
    private string name_ = "";
    /// <summary>
    /// 角色名称
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "is_custom_avatar" field.</summary>
    public const int IsCustomAvatarFieldNumber = 4;
    private bool isCustomAvatar_;
    /// <summary>
    /// 是否是自定义头像
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsCustomAvatar {
      get { return isCustomAvatar_; }
      set {
        isCustomAvatar_ = value;
      }
    }

    /// <summary>Field number for the "head_system_avatar" field.</summary>
    public const int HeadSystemAvatarFieldNumber = 5;
    private uint headSystemAvatar_;
    /// <summary>
    /// 角色头像图标
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint HeadSystemAvatar {
      get { return headSystemAvatar_; }
      set {
        headSystemAvatar_ = value;
      }
    }

    /// <summary>Field number for the "head_custom_avatar" field.</summary>
    public const int HeadCustomAvatarFieldNumber = 6;
    private string headCustomAvatar_ = "";
    /// <summary>
    /// 角色自定义头像链接地址
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string HeadCustomAvatar {
      get { return headCustomAvatar_; }
      set {
        headCustomAvatar_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "head_border" field.</summary>
    public const int HeadBorderFieldNumber = 7;
    private uint headBorder_;
    /// <summary>
    /// 角色头像框
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint HeadBorder {
      get { return headBorder_; }
      set {
        headBorder_ = value;
      }
    }

    /// <summary>Field number for the "power" field.</summary>
    public const int PowerFieldNumber = 8;
    private ulong power_;
    /// <summary>
    /// 总战力
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong Power {
      get { return power_; }
      set {
        power_ = value;
      }
    }

    /// <summary>Field number for the "last_login_at" field.</summary>
    public const int LastLoginAtFieldNumber = 9;
    private long lastLoginAt_;
    /// <summary>
    /// 最后登录时间戳，秒
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long LastLoginAt {
      get { return lastLoginAt_; }
      set {
        lastLoginAt_ = value;
      }
    }

    /// <summary>Field number for the "last_logout_at" field.</summary>
    public const int LastLogoutAtFieldNumber = 10;
    private long lastLogoutAt_;
    /// <summary>
    /// 最后登出时间戳，秒
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long LastLogoutAt {
      get { return lastLogoutAt_; }
      set {
        lastLogoutAt_ = value;
      }
    }

    /// <summary>Field number for the "create_at" field.</summary>
    public const int CreateAtFieldNumber = 11;
    private long createAt_;
    /// <summary>
    /// 角色创建时间戳，秒
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long CreateAt {
      get { return createAt_; }
      set {
        createAt_ = value;
      }
    }

    /// <summary>Field number for the "lang" field.</summary>
    public const int LangFieldNumber = 12;
    private int lang_;
    /// <summary>
    /// 玩家当前语言 language_type.xlsx - id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Lang {
      get { return lang_; }
      set {
        lang_ = value;
      }
    }

    /// <summary>Field number for the "gender" field.</summary>
    public const int GenderFieldNumber = 13;
    private uint gender_;
    /// <summary>
    /// 性别，0 未知，1 男，2 女
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Gender {
      get { return gender_; }
      set {
        gender_ = value;
      }
    }

    /// <summary>Field number for the "kill_solder_num" field.</summary>
    public const int KillSolderNumFieldNumber = 14;
    private ulong killSolderNum_;
    /// <summary>
    /// 击败士兵数量的总和
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong KillSolderNum {
      get { return killSolderNum_; }
      set {
        killSolderNum_ = value;
      }
    }

    /// <summary>Field number for the "inner_city_grid_id" field.</summary>
    public const int InnerCityGridIdFieldNumber = 15;
    private int innerCityGridId_;
    /// <summary>
    /// 当前格子 id，默认 1, innercity_path.xlsx - id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int InnerCityGridId {
      get { return innerCityGridId_; }
      set {
        innerCityGridId_ = value;
      }
    }

    /// <summary>Field number for the "inner_city_region_id" field.</summary>
    public const int InnerCityRegionIdFieldNumber = 16;
    private int innerCityRegionId_;
    /// <summary>
    /// 已解锁大格子 id，默认 0, innercity_areaunlock.xlsx - id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int InnerCityRegionId {
      get { return innerCityRegionId_; }
      set {
        innerCityRegionId_ = value;
      }
    }

    /// <summary>Field number for the "vip_level" field.</summary>
    public const int VipLevelFieldNumber = 17;
    private int vipLevel_;
    /// <summary>
    /// vip 等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int VipLevel {
      get { return vipLevel_; }
      set {
        vipLevel_ = value;
      }
    }

    /// <summary>Field number for the "vip_exp" field.</summary>
    public const int VipExpFieldNumber = 18;
    private int vipExp_;
    /// <summary>
    /// vip 经验
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int VipExp {
      get { return vipExp_; }
      set {
        vipExp_ = value;
      }
    }

    /// <summary>Field number for the "vip_end_time" field.</summary>
    public const int VipEndTimeFieldNumber = 19;
    private long vipEndTime_;
    /// <summary>
    /// vip 过期时间戳
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long VipEndTime {
      get { return vipEndTime_; }
      set {
        vipEndTime_ = value;
      }
    }

    /// <summary>Field number for the "is_vip_daily_point_receive" field.</summary>
    public const int IsVipDailyPointReceiveFieldNumber = 20;
    private bool isVipDailyPointReceive_;
    /// <summary>
    /// 是否领取了每日免费vip点数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsVipDailyPointReceive {
      get { return isVipDailyPointReceive_; }
      set {
        isVipDailyPointReceive_ = value;
      }
    }

    /// <summary>Field number for the "is_vip_daily_gift_receive" field.</summary>
    public const int IsVipDailyGiftReceiveFieldNumber = 21;
    private bool isVipDailyGiftReceive_;
    /// <summary>
    /// 是否领取了每日免费vip礼包
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsVipDailyGiftReceive {
      get { return isVipDailyGiftReceive_; }
      set {
        isVipDailyGiftReceive_ = value;
      }
    }

    /// <summary>Field number for the "vip_today_buy_points" field.</summary>
    public const int VipTodayBuyPointsFieldNumber = 22;
    private int vipTodayBuyPoints_;
    /// <summary>
    /// vip今日购买的点数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int VipTodayBuyPoints {
      get { return vipTodayBuyPoints_; }
      set {
        vipTodayBuyPoints_ = value;
      }
    }

    /// <summary>Field number for the "login_continue_days" field.</summary>
    public const int LoginContinueDaysFieldNumber = 23;
    private int loginContinueDays_;
    /// <summary>
    /// 连续登录天数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int LoginContinueDays {
      get { return loginContinueDays_; }
      set {
        loginContinueDays_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RoleAttr);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RoleAttr other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ServerId != other.ServerId) return false;
      if (ServerName != other.ServerName) return false;
      if (Name != other.Name) return false;
      if (IsCustomAvatar != other.IsCustomAvatar) return false;
      if (HeadSystemAvatar != other.HeadSystemAvatar) return false;
      if (HeadCustomAvatar != other.HeadCustomAvatar) return false;
      if (HeadBorder != other.HeadBorder) return false;
      if (Power != other.Power) return false;
      if (LastLoginAt != other.LastLoginAt) return false;
      if (LastLogoutAt != other.LastLogoutAt) return false;
      if (CreateAt != other.CreateAt) return false;
      if (Lang != other.Lang) return false;
      if (Gender != other.Gender) return false;
      if (KillSolderNum != other.KillSolderNum) return false;
      if (InnerCityGridId != other.InnerCityGridId) return false;
      if (InnerCityRegionId != other.InnerCityRegionId) return false;
      if (VipLevel != other.VipLevel) return false;
      if (VipExp != other.VipExp) return false;
      if (VipEndTime != other.VipEndTime) return false;
      if (IsVipDailyPointReceive != other.IsVipDailyPointReceive) return false;
      if (IsVipDailyGiftReceive != other.IsVipDailyGiftReceive) return false;
      if (VipTodayBuyPoints != other.VipTodayBuyPoints) return false;
      if (LoginContinueDays != other.LoginContinueDays) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ServerId != 0) hash ^= ServerId.GetHashCode();
      if (ServerName.Length != 0) hash ^= ServerName.GetHashCode();
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (IsCustomAvatar != false) hash ^= IsCustomAvatar.GetHashCode();
      if (HeadSystemAvatar != 0) hash ^= HeadSystemAvatar.GetHashCode();
      if (HeadCustomAvatar.Length != 0) hash ^= HeadCustomAvatar.GetHashCode();
      if (HeadBorder != 0) hash ^= HeadBorder.GetHashCode();
      if (Power != 0UL) hash ^= Power.GetHashCode();
      if (LastLoginAt != 0L) hash ^= LastLoginAt.GetHashCode();
      if (LastLogoutAt != 0L) hash ^= LastLogoutAt.GetHashCode();
      if (CreateAt != 0L) hash ^= CreateAt.GetHashCode();
      if (Lang != 0) hash ^= Lang.GetHashCode();
      if (Gender != 0) hash ^= Gender.GetHashCode();
      if (KillSolderNum != 0UL) hash ^= KillSolderNum.GetHashCode();
      if (InnerCityGridId != 0) hash ^= InnerCityGridId.GetHashCode();
      if (InnerCityRegionId != 0) hash ^= InnerCityRegionId.GetHashCode();
      if (VipLevel != 0) hash ^= VipLevel.GetHashCode();
      if (VipExp != 0) hash ^= VipExp.GetHashCode();
      if (VipEndTime != 0L) hash ^= VipEndTime.GetHashCode();
      if (IsVipDailyPointReceive != false) hash ^= IsVipDailyPointReceive.GetHashCode();
      if (IsVipDailyGiftReceive != false) hash ^= IsVipDailyGiftReceive.GetHashCode();
      if (VipTodayBuyPoints != 0) hash ^= VipTodayBuyPoints.GetHashCode();
      if (LoginContinueDays != 0) hash ^= LoginContinueDays.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ServerId != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(ServerId);
      }
      if (ServerName.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ServerName);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Name);
      }
      if (IsCustomAvatar != false) {
        output.WriteRawTag(32);
        output.WriteBool(IsCustomAvatar);
      }
      if (HeadSystemAvatar != 0) {
        output.WriteRawTag(40);
        output.WriteUInt32(HeadSystemAvatar);
      }
      if (HeadCustomAvatar.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(HeadCustomAvatar);
      }
      if (HeadBorder != 0) {
        output.WriteRawTag(56);
        output.WriteUInt32(HeadBorder);
      }
      if (Power != 0UL) {
        output.WriteRawTag(64);
        output.WriteUInt64(Power);
      }
      if (LastLoginAt != 0L) {
        output.WriteRawTag(72);
        output.WriteInt64(LastLoginAt);
      }
      if (LastLogoutAt != 0L) {
        output.WriteRawTag(80);
        output.WriteInt64(LastLogoutAt);
      }
      if (CreateAt != 0L) {
        output.WriteRawTag(88);
        output.WriteInt64(CreateAt);
      }
      if (Lang != 0) {
        output.WriteRawTag(96);
        output.WriteInt32(Lang);
      }
      if (Gender != 0) {
        output.WriteRawTag(104);
        output.WriteUInt32(Gender);
      }
      if (KillSolderNum != 0UL) {
        output.WriteRawTag(112);
        output.WriteUInt64(KillSolderNum);
      }
      if (InnerCityGridId != 0) {
        output.WriteRawTag(120);
        output.WriteInt32(InnerCityGridId);
      }
      if (InnerCityRegionId != 0) {
        output.WriteRawTag(128, 1);
        output.WriteInt32(InnerCityRegionId);
      }
      if (VipLevel != 0) {
        output.WriteRawTag(136, 1);
        output.WriteInt32(VipLevel);
      }
      if (VipExp != 0) {
        output.WriteRawTag(144, 1);
        output.WriteInt32(VipExp);
      }
      if (VipEndTime != 0L) {
        output.WriteRawTag(152, 1);
        output.WriteInt64(VipEndTime);
      }
      if (IsVipDailyPointReceive != false) {
        output.WriteRawTag(160, 1);
        output.WriteBool(IsVipDailyPointReceive);
      }
      if (IsVipDailyGiftReceive != false) {
        output.WriteRawTag(168, 1);
        output.WriteBool(IsVipDailyGiftReceive);
      }
      if (VipTodayBuyPoints != 0) {
        output.WriteRawTag(176, 1);
        output.WriteInt32(VipTodayBuyPoints);
      }
      if (LoginContinueDays != 0) {
        output.WriteRawTag(184, 1);
        output.WriteInt32(LoginContinueDays);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ServerId != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(ServerId);
      }
      if (ServerName.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ServerName);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Name);
      }
      if (IsCustomAvatar != false) {
        output.WriteRawTag(32);
        output.WriteBool(IsCustomAvatar);
      }
      if (HeadSystemAvatar != 0) {
        output.WriteRawTag(40);
        output.WriteUInt32(HeadSystemAvatar);
      }
      if (HeadCustomAvatar.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(HeadCustomAvatar);
      }
      if (HeadBorder != 0) {
        output.WriteRawTag(56);
        output.WriteUInt32(HeadBorder);
      }
      if (Power != 0UL) {
        output.WriteRawTag(64);
        output.WriteUInt64(Power);
      }
      if (LastLoginAt != 0L) {
        output.WriteRawTag(72);
        output.WriteInt64(LastLoginAt);
      }
      if (LastLogoutAt != 0L) {
        output.WriteRawTag(80);
        output.WriteInt64(LastLogoutAt);
      }
      if (CreateAt != 0L) {
        output.WriteRawTag(88);
        output.WriteInt64(CreateAt);
      }
      if (Lang != 0) {
        output.WriteRawTag(96);
        output.WriteInt32(Lang);
      }
      if (Gender != 0) {
        output.WriteRawTag(104);
        output.WriteUInt32(Gender);
      }
      if (KillSolderNum != 0UL) {
        output.WriteRawTag(112);
        output.WriteUInt64(KillSolderNum);
      }
      if (InnerCityGridId != 0) {
        output.WriteRawTag(120);
        output.WriteInt32(InnerCityGridId);
      }
      if (InnerCityRegionId != 0) {
        output.WriteRawTag(128, 1);
        output.WriteInt32(InnerCityRegionId);
      }
      if (VipLevel != 0) {
        output.WriteRawTag(136, 1);
        output.WriteInt32(VipLevel);
      }
      if (VipExp != 0) {
        output.WriteRawTag(144, 1);
        output.WriteInt32(VipExp);
      }
      if (VipEndTime != 0L) {
        output.WriteRawTag(152, 1);
        output.WriteInt64(VipEndTime);
      }
      if (IsVipDailyPointReceive != false) {
        output.WriteRawTag(160, 1);
        output.WriteBool(IsVipDailyPointReceive);
      }
      if (IsVipDailyGiftReceive != false) {
        output.WriteRawTag(168, 1);
        output.WriteBool(IsVipDailyGiftReceive);
      }
      if (VipTodayBuyPoints != 0) {
        output.WriteRawTag(176, 1);
        output.WriteInt32(VipTodayBuyPoints);
      }
      if (LoginContinueDays != 0) {
        output.WriteRawTag(184, 1);
        output.WriteInt32(LoginContinueDays);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ServerId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(ServerId);
      }
      if (ServerName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ServerName);
      }
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (IsCustomAvatar != false) {
        size += 1 + 1;
      }
      if (HeadSystemAvatar != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(HeadSystemAvatar);
      }
      if (HeadCustomAvatar.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(HeadCustomAvatar);
      }
      if (HeadBorder != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(HeadBorder);
      }
      if (Power != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(Power);
      }
      if (LastLoginAt != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(LastLoginAt);
      }
      if (LastLogoutAt != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(LastLogoutAt);
      }
      if (CreateAt != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(CreateAt);
      }
      if (Lang != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Lang);
      }
      if (Gender != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Gender);
      }
      if (KillSolderNum != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(KillSolderNum);
      }
      if (InnerCityGridId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(InnerCityGridId);
      }
      if (InnerCityRegionId != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(InnerCityRegionId);
      }
      if (VipLevel != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(VipLevel);
      }
      if (VipExp != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(VipExp);
      }
      if (VipEndTime != 0L) {
        size += 2 + pb::CodedOutputStream.ComputeInt64Size(VipEndTime);
      }
      if (IsVipDailyPointReceive != false) {
        size += 2 + 1;
      }
      if (IsVipDailyGiftReceive != false) {
        size += 2 + 1;
      }
      if (VipTodayBuyPoints != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(VipTodayBuyPoints);
      }
      if (LoginContinueDays != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(LoginContinueDays);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RoleAttr other) {
      if (other == null) {
        return;
      }
      if (other.ServerId != 0) {
        ServerId = other.ServerId;
      }
      if (other.ServerName.Length != 0) {
        ServerName = other.ServerName;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.IsCustomAvatar != false) {
        IsCustomAvatar = other.IsCustomAvatar;
      }
      if (other.HeadSystemAvatar != 0) {
        HeadSystemAvatar = other.HeadSystemAvatar;
      }
      if (other.HeadCustomAvatar.Length != 0) {
        HeadCustomAvatar = other.HeadCustomAvatar;
      }
      if (other.HeadBorder != 0) {
        HeadBorder = other.HeadBorder;
      }
      if (other.Power != 0UL) {
        Power = other.Power;
      }
      if (other.LastLoginAt != 0L) {
        LastLoginAt = other.LastLoginAt;
      }
      if (other.LastLogoutAt != 0L) {
        LastLogoutAt = other.LastLogoutAt;
      }
      if (other.CreateAt != 0L) {
        CreateAt = other.CreateAt;
      }
      if (other.Lang != 0) {
        Lang = other.Lang;
      }
      if (other.Gender != 0) {
        Gender = other.Gender;
      }
      if (other.KillSolderNum != 0UL) {
        KillSolderNum = other.KillSolderNum;
      }
      if (other.InnerCityGridId != 0) {
        InnerCityGridId = other.InnerCityGridId;
      }
      if (other.InnerCityRegionId != 0) {
        InnerCityRegionId = other.InnerCityRegionId;
      }
      if (other.VipLevel != 0) {
        VipLevel = other.VipLevel;
      }
      if (other.VipExp != 0) {
        VipExp = other.VipExp;
      }
      if (other.VipEndTime != 0L) {
        VipEndTime = other.VipEndTime;
      }
      if (other.IsVipDailyPointReceive != false) {
        IsVipDailyPointReceive = other.IsVipDailyPointReceive;
      }
      if (other.IsVipDailyGiftReceive != false) {
        IsVipDailyGiftReceive = other.IsVipDailyGiftReceive;
      }
      if (other.VipTodayBuyPoints != 0) {
        VipTodayBuyPoints = other.VipTodayBuyPoints;
      }
      if (other.LoginContinueDays != 0) {
        LoginContinueDays = other.LoginContinueDays;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            ServerId = input.ReadUInt32();
            break;
          }
          case 18: {
            ServerName = input.ReadString();
            break;
          }
          case 26: {
            Name = input.ReadString();
            break;
          }
          case 32: {
            IsCustomAvatar = input.ReadBool();
            break;
          }
          case 40: {
            HeadSystemAvatar = input.ReadUInt32();
            break;
          }
          case 50: {
            HeadCustomAvatar = input.ReadString();
            break;
          }
          case 56: {
            HeadBorder = input.ReadUInt32();
            break;
          }
          case 64: {
            Power = input.ReadUInt64();
            break;
          }
          case 72: {
            LastLoginAt = input.ReadInt64();
            break;
          }
          case 80: {
            LastLogoutAt = input.ReadInt64();
            break;
          }
          case 88: {
            CreateAt = input.ReadInt64();
            break;
          }
          case 96: {
            Lang = input.ReadInt32();
            break;
          }
          case 104: {
            Gender = input.ReadUInt32();
            break;
          }
          case 112: {
            KillSolderNum = input.ReadUInt64();
            break;
          }
          case 120: {
            InnerCityGridId = input.ReadInt32();
            break;
          }
          case 128: {
            InnerCityRegionId = input.ReadInt32();
            break;
          }
          case 136: {
            VipLevel = input.ReadInt32();
            break;
          }
          case 144: {
            VipExp = input.ReadInt32();
            break;
          }
          case 152: {
            VipEndTime = input.ReadInt64();
            break;
          }
          case 160: {
            IsVipDailyPointReceive = input.ReadBool();
            break;
          }
          case 168: {
            IsVipDailyGiftReceive = input.ReadBool();
            break;
          }
          case 176: {
            VipTodayBuyPoints = input.ReadInt32();
            break;
          }
          case 184: {
            LoginContinueDays = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            ServerId = input.ReadUInt32();
            break;
          }
          case 18: {
            ServerName = input.ReadString();
            break;
          }
          case 26: {
            Name = input.ReadString();
            break;
          }
          case 32: {
            IsCustomAvatar = input.ReadBool();
            break;
          }
          case 40: {
            HeadSystemAvatar = input.ReadUInt32();
            break;
          }
          case 50: {
            HeadCustomAvatar = input.ReadString();
            break;
          }
          case 56: {
            HeadBorder = input.ReadUInt32();
            break;
          }
          case 64: {
            Power = input.ReadUInt64();
            break;
          }
          case 72: {
            LastLoginAt = input.ReadInt64();
            break;
          }
          case 80: {
            LastLogoutAt = input.ReadInt64();
            break;
          }
          case 88: {
            CreateAt = input.ReadInt64();
            break;
          }
          case 96: {
            Lang = input.ReadInt32();
            break;
          }
          case 104: {
            Gender = input.ReadUInt32();
            break;
          }
          case 112: {
            KillSolderNum = input.ReadUInt64();
            break;
          }
          case 120: {
            InnerCityGridId = input.ReadInt32();
            break;
          }
          case 128: {
            InnerCityRegionId = input.ReadInt32();
            break;
          }
          case 136: {
            VipLevel = input.ReadInt32();
            break;
          }
          case 144: {
            VipExp = input.ReadInt32();
            break;
          }
          case 152: {
            VipEndTime = input.ReadInt64();
            break;
          }
          case 160: {
            IsVipDailyPointReceive = input.ReadBool();
            break;
          }
          case 168: {
            IsVipDailyGiftReceive = input.ReadBool();
            break;
          }
          case 176: {
            VipTodayBuyPoints = input.ReadInt32();
            break;
          }
          case 184: {
            LoginContinueDays = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// RoleBuild 角色建筑
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RoleBuild : pb::IMessage<RoleBuild>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RoleBuild> _parser = new pb::MessageParser<RoleBuild>(() => new RoleBuild());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RoleBuild> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Roledata.RoledataReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleBuild() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleBuild(RoleBuild other) : this() {
      builds_ = other.builds_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleBuild Clone() {
      return new RoleBuild(this);
    }

    /// <summary>Field number for the "builds" field.</summary>
    public const int BuildsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Build.Build> _repeated_builds_codec
        = pb::FieldCodec.ForMessage(10, global::Build.Build.Parser);
    private readonly pbc::RepeatedField<global::Build.Build> builds_ = new pbc::RepeatedField<global::Build.Build>();
    /// <summary>
    /// 建筑列表，不包含未解锁的建筑
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Build.Build> Builds {
      get { return builds_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RoleBuild);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RoleBuild other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!builds_.Equals(other.builds_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= builds_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      builds_.WriteTo(output, _repeated_builds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      builds_.WriteTo(ref output, _repeated_builds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += builds_.CalculateSize(_repeated_builds_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RoleBuild other) {
      if (other == null) {
        return;
      }
      builds_.Add(other.builds_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            builds_.AddEntriesFrom(input, _repeated_builds_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            builds_.AddEntriesFrom(ref input, _repeated_builds_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// RoleUnion 角色联盟
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RoleUnion : pb::IMessage<RoleUnion>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RoleUnion> _parser = new pb::MessageParser<RoleUnion>(() => new RoleUnion());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RoleUnion> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Roledata.RoledataReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleUnion() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleUnion(RoleUnion other) : this() {
      unionId_ = other.unionId_;
      unionPosition_ = other.unionPosition_;
      unionPermission_ = other.unionPermission_;
      unionLeaveTime_ = other.unionLeaveTime_;
      isFirstJoinUnion_ = other.isFirstJoinUnion_;
      showSpecialView_ = other.showSpecialView_;
      isOpenUnion_ = other.isOpenUnion_;
      unionJoinTime_ = other.unionJoinTime_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleUnion Clone() {
      return new RoleUnion(this);
    }

    /// <summary>Field number for the "union_id" field.</summary>
    public const int UnionIdFieldNumber = 1;
    private ulong unionId_;
    /// <summary>
    /// 联盟id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong UnionId {
      get { return unionId_; }
      set {
        unionId_ = value;
      }
    }

    /// <summary>Field number for the "union_position" field.</summary>
    public const int UnionPositionFieldNumber = 2;
    private int unionPosition_;
    /// <summary>
    /// 联盟职位
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int UnionPosition {
      get { return unionPosition_; }
      set {
        unionPosition_ = value;
      }
    }

    /// <summary>Field number for the "union_permission" field.</summary>
    public const int UnionPermissionFieldNumber = 3;
    private int unionPermission_;
    /// <summary>
    /// 联盟权限
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int UnionPermission {
      get { return unionPermission_; }
      set {
        unionPermission_ = value;
      }
    }

    /// <summary>Field number for the "union_leave_time" field.</summary>
    public const int UnionLeaveTimeFieldNumber = 4;
    private long unionLeaveTime_;
    /// <summary>
    /// 离开联盟时间戳
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long UnionLeaveTime {
      get { return unionLeaveTime_; }
      set {
        unionLeaveTime_ = value;
      }
    }

    /// <summary>Field number for the "is_first_join_union" field.</summary>
    public const int IsFirstJoinUnionFieldNumber = 5;
    private bool isFirstJoinUnion_;
    /// <summary>
    /// 是否第一次加入联盟
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsFirstJoinUnion {
      get { return isFirstJoinUnion_; }
      set {
        isFirstJoinUnion_ = value;
      }
    }

    /// <summary>Field number for the "show_special_view" field.</summary>
    public const int ShowSpecialViewFieldNumber = 6;
    private bool showSpecialView_;
    /// <summary>
    /// 是否显示特殊联盟界面（玩家未加入过联盟且解锁联盟功能在24小时内时，会弹出特殊界面）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool ShowSpecialView {
      get { return showSpecialView_; }
      set {
        showSpecialView_ = value;
      }
    }

    /// <summary>Field number for the "is_open_union" field.</summary>
    public const int IsOpenUnionFieldNumber = 7;
    private bool isOpenUnion_;
    /// <summary>
    /// 是否开启联盟（同盟中心是否建造）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsOpenUnion {
      get { return isOpenUnion_; }
      set {
        isOpenUnion_ = value;
      }
    }

    /// <summary>Field number for the "union_join_time" field.</summary>
    public const int UnionJoinTimeFieldNumber = 8;
    private long unionJoinTime_;
    /// <summary>
    /// 加入联盟时间戳
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long UnionJoinTime {
      get { return unionJoinTime_; }
      set {
        unionJoinTime_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RoleUnion);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RoleUnion other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (UnionId != other.UnionId) return false;
      if (UnionPosition != other.UnionPosition) return false;
      if (UnionPermission != other.UnionPermission) return false;
      if (UnionLeaveTime != other.UnionLeaveTime) return false;
      if (IsFirstJoinUnion != other.IsFirstJoinUnion) return false;
      if (ShowSpecialView != other.ShowSpecialView) return false;
      if (IsOpenUnion != other.IsOpenUnion) return false;
      if (UnionJoinTime != other.UnionJoinTime) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (UnionId != 0UL) hash ^= UnionId.GetHashCode();
      if (UnionPosition != 0) hash ^= UnionPosition.GetHashCode();
      if (UnionPermission != 0) hash ^= UnionPermission.GetHashCode();
      if (UnionLeaveTime != 0L) hash ^= UnionLeaveTime.GetHashCode();
      if (IsFirstJoinUnion != false) hash ^= IsFirstJoinUnion.GetHashCode();
      if (ShowSpecialView != false) hash ^= ShowSpecialView.GetHashCode();
      if (IsOpenUnion != false) hash ^= IsOpenUnion.GetHashCode();
      if (UnionJoinTime != 0L) hash ^= UnionJoinTime.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (UnionId != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(UnionId);
      }
      if (UnionPosition != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(UnionPosition);
      }
      if (UnionPermission != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(UnionPermission);
      }
      if (UnionLeaveTime != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(UnionLeaveTime);
      }
      if (IsFirstJoinUnion != false) {
        output.WriteRawTag(40);
        output.WriteBool(IsFirstJoinUnion);
      }
      if (ShowSpecialView != false) {
        output.WriteRawTag(48);
        output.WriteBool(ShowSpecialView);
      }
      if (IsOpenUnion != false) {
        output.WriteRawTag(56);
        output.WriteBool(IsOpenUnion);
      }
      if (UnionJoinTime != 0L) {
        output.WriteRawTag(64);
        output.WriteInt64(UnionJoinTime);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (UnionId != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(UnionId);
      }
      if (UnionPosition != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(UnionPosition);
      }
      if (UnionPermission != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(UnionPermission);
      }
      if (UnionLeaveTime != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(UnionLeaveTime);
      }
      if (IsFirstJoinUnion != false) {
        output.WriteRawTag(40);
        output.WriteBool(IsFirstJoinUnion);
      }
      if (ShowSpecialView != false) {
        output.WriteRawTag(48);
        output.WriteBool(ShowSpecialView);
      }
      if (IsOpenUnion != false) {
        output.WriteRawTag(56);
        output.WriteBool(IsOpenUnion);
      }
      if (UnionJoinTime != 0L) {
        output.WriteRawTag(64);
        output.WriteInt64(UnionJoinTime);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (UnionId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(UnionId);
      }
      if (UnionPosition != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(UnionPosition);
      }
      if (UnionPermission != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(UnionPermission);
      }
      if (UnionLeaveTime != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(UnionLeaveTime);
      }
      if (IsFirstJoinUnion != false) {
        size += 1 + 1;
      }
      if (ShowSpecialView != false) {
        size += 1 + 1;
      }
      if (IsOpenUnion != false) {
        size += 1 + 1;
      }
      if (UnionJoinTime != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(UnionJoinTime);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RoleUnion other) {
      if (other == null) {
        return;
      }
      if (other.UnionId != 0UL) {
        UnionId = other.UnionId;
      }
      if (other.UnionPosition != 0) {
        UnionPosition = other.UnionPosition;
      }
      if (other.UnionPermission != 0) {
        UnionPermission = other.UnionPermission;
      }
      if (other.UnionLeaveTime != 0L) {
        UnionLeaveTime = other.UnionLeaveTime;
      }
      if (other.IsFirstJoinUnion != false) {
        IsFirstJoinUnion = other.IsFirstJoinUnion;
      }
      if (other.ShowSpecialView != false) {
        ShowSpecialView = other.ShowSpecialView;
      }
      if (other.IsOpenUnion != false) {
        IsOpenUnion = other.IsOpenUnion;
      }
      if (other.UnionJoinTime != 0L) {
        UnionJoinTime = other.UnionJoinTime;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            UnionId = input.ReadUInt64();
            break;
          }
          case 16: {
            UnionPosition = input.ReadInt32();
            break;
          }
          case 24: {
            UnionPermission = input.ReadInt32();
            break;
          }
          case 32: {
            UnionLeaveTime = input.ReadInt64();
            break;
          }
          case 40: {
            IsFirstJoinUnion = input.ReadBool();
            break;
          }
          case 48: {
            ShowSpecialView = input.ReadBool();
            break;
          }
          case 56: {
            IsOpenUnion = input.ReadBool();
            break;
          }
          case 64: {
            UnionJoinTime = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            UnionId = input.ReadUInt64();
            break;
          }
          case 16: {
            UnionPosition = input.ReadInt32();
            break;
          }
          case 24: {
            UnionPermission = input.ReadInt32();
            break;
          }
          case 32: {
            UnionLeaveTime = input.ReadInt64();
            break;
          }
          case 40: {
            IsFirstJoinUnion = input.ReadBool();
            break;
          }
          case 48: {
            ShowSpecialView = input.ReadBool();
            break;
          }
          case 56: {
            IsOpenUnion = input.ReadBool();
            break;
          }
          case 64: {
            UnionJoinTime = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// RoleItem 角色道具
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RoleItem : pb::IMessage<RoleItem>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RoleItem> _parser = new pb::MessageParser<RoleItem>(() => new RoleItem());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RoleItem> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Roledata.RoledataReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleItem() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleItem(RoleItem other) : this() {
      items_ = other.items_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleItem Clone() {
      return new RoleItem(this);
    }

    /// <summary>Field number for the "items" field.</summary>
    public const int ItemsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Item.Item> _repeated_items_codec
        = pb::FieldCodec.ForMessage(10, global::Item.Item.Parser);
    private readonly pbc::RepeatedField<global::Item.Item> items_ = new pbc::RepeatedField<global::Item.Item>();
    /// <summary>
    /// 道具列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Item.Item> Items {
      get { return items_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RoleItem);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RoleItem other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!items_.Equals(other.items_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= items_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      items_.WriteTo(output, _repeated_items_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      items_.WriteTo(ref output, _repeated_items_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += items_.CalculateSize(_repeated_items_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RoleItem other) {
      if (other == null) {
        return;
      }
      items_.Add(other.items_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            items_.AddEntriesFrom(input, _repeated_items_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            items_.AddEntriesFrom(ref input, _repeated_items_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// RoleHero 角色英雄
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RoleHero : pb::IMessage<RoleHero>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RoleHero> _parser = new pb::MessageParser<RoleHero>(() => new RoleHero());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RoleHero> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Roledata.RoledataReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleHero() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleHero(RoleHero other) : this() {
      heroes_ = other.heroes_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleHero Clone() {
      return new RoleHero(this);
    }

    /// <summary>Field number for the "heroes" field.</summary>
    public const int HeroesFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Hero.Hero> _repeated_heroes_codec
        = pb::FieldCodec.ForMessage(10, global::Hero.Hero.Parser);
    private readonly pbc::RepeatedField<global::Hero.Hero> heroes_ = new pbc::RepeatedField<global::Hero.Hero>();
    /// <summary>
    /// 英雄列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Hero.Hero> Heroes {
      get { return heroes_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RoleHero);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RoleHero other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!heroes_.Equals(other.heroes_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= heroes_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      heroes_.WriteTo(output, _repeated_heroes_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      heroes_.WriteTo(ref output, _repeated_heroes_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += heroes_.CalculateSize(_repeated_heroes_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RoleHero other) {
      if (other == null) {
        return;
      }
      heroes_.Add(other.heroes_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            heroes_.AddEntriesFrom(input, _repeated_heroes_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            heroes_.AddEntriesFrom(ref input, _repeated_heroes_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// RoleEquip 角色装备
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RoleEquip : pb::IMessage<RoleEquip>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RoleEquip> _parser = new pb::MessageParser<RoleEquip>(() => new RoleEquip());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RoleEquip> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Roledata.RoledataReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleEquip() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleEquip(RoleEquip other) : this() {
      equips_ = other.equips_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleEquip Clone() {
      return new RoleEquip(this);
    }

    /// <summary>Field number for the "equips" field.</summary>
    public const int EquipsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Equip.Equip> _repeated_equips_codec
        = pb::FieldCodec.ForMessage(10, global::Equip.Equip.Parser);
    private readonly pbc::RepeatedField<global::Equip.Equip> equips_ = new pbc::RepeatedField<global::Equip.Equip>();
    /// <summary>
    /// 装备列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Equip.Equip> Equips {
      get { return equips_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RoleEquip);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RoleEquip other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!equips_.Equals(other.equips_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= equips_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      equips_.WriteTo(output, _repeated_equips_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      equips_.WriteTo(ref output, _repeated_equips_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += equips_.CalculateSize(_repeated_equips_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RoleEquip other) {
      if (other == null) {
        return;
      }
      equips_.Add(other.equips_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            equips_.AddEntriesFrom(input, _repeated_equips_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            equips_.AddEntriesFrom(ref input, _repeated_equips_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// RoleQueue 
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RoleQueue : pb::IMessage<RoleQueue>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RoleQueue> _parser = new pb::MessageParser<RoleQueue>(() => new RoleQueue());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RoleQueue> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Roledata.RoledataReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleQueue() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleQueue(RoleQueue other) : this() {
      queues_ = other.queues_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleQueue Clone() {
      return new RoleQueue(this);
    }

    /// <summary>Field number for the "queues" field.</summary>
    public const int QueuesFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Build.Queue> _repeated_queues_codec
        = pb::FieldCodec.ForMessage(10, global::Build.Queue.Parser);
    private readonly pbc::RepeatedField<global::Build.Queue> queues_ = new pbc::RepeatedField<global::Build.Queue>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Build.Queue> Queues {
      get { return queues_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RoleQueue);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RoleQueue other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!queues_.Equals(other.queues_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= queues_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      queues_.WriteTo(output, _repeated_queues_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      queues_.WriteTo(ref output, _repeated_queues_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += queues_.CalculateSize(_repeated_queues_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RoleQueue other) {
      if (other == null) {
        return;
      }
      queues_.Add(other.queues_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            queues_.AddEntriesFrom(input, _repeated_queues_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            queues_.AddEntriesFrom(ref input, _repeated_queues_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RoleBuildWorker : pb::IMessage<RoleBuildWorker>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RoleBuildWorker> _parser = new pb::MessageParser<RoleBuildWorker>(() => new RoleBuildWorker());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RoleBuildWorker> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Roledata.RoledataReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleBuildWorker() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleBuildWorker(RoleBuildWorker other) : this() {
      workers_ = other.workers_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleBuildWorker Clone() {
      return new RoleBuildWorker(this);
    }

    /// <summary>Field number for the "workers" field.</summary>
    public const int WorkersFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Worker.Worker> _repeated_workers_codec
        = pb::FieldCodec.ForMessage(10, global::Worker.Worker.Parser);
    private readonly pbc::RepeatedField<global::Worker.Worker> workers_ = new pbc::RepeatedField<global::Worker.Worker>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Worker.Worker> Workers {
      get { return workers_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RoleBuildWorker);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RoleBuildWorker other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!workers_.Equals(other.workers_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= workers_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      workers_.WriteTo(output, _repeated_workers_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      workers_.WriteTo(ref output, _repeated_workers_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += workers_.CalculateSize(_repeated_workers_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RoleBuildWorker other) {
      if (other == null) {
        return;
      }
      workers_.Add(other.workers_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            workers_.AddEntriesFrom(input, _repeated_workers_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            workers_.AddEntriesFrom(ref input, _repeated_workers_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RoleSoldier : pb::IMessage<RoleSoldier>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RoleSoldier> _parser = new pb::MessageParser<RoleSoldier>(() => new RoleSoldier());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RoleSoldier> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Roledata.RoledataReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleSoldier() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleSoldier(RoleSoldier other) : this() {
      soldiers_ = other.soldiers_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleSoldier Clone() {
      return new RoleSoldier(this);
    }

    /// <summary>Field number for the "soldiers" field.</summary>
    public const int SoldiersFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Build.Soldier> _repeated_soldiers_codec
        = pb::FieldCodec.ForMessage(10, global::Build.Soldier.Parser);
    private readonly pbc::RepeatedField<global::Build.Soldier> soldiers_ = new pbc::RepeatedField<global::Build.Soldier>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Build.Soldier> Soldiers {
      get { return soldiers_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RoleSoldier);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RoleSoldier other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!soldiers_.Equals(other.soldiers_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= soldiers_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      soldiers_.WriteTo(output, _repeated_soldiers_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      soldiers_.WriteTo(ref output, _repeated_soldiers_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += soldiers_.CalculateSize(_repeated_soldiers_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RoleSoldier other) {
      if (other == null) {
        return;
      }
      soldiers_.Add(other.soldiers_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            soldiers_.AddEntriesFrom(input, _repeated_soldiers_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            soldiers_.AddEntriesFrom(ref input, _repeated_soldiers_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RoleSurvivor : pb::IMessage<RoleSurvivor>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RoleSurvivor> _parser = new pb::MessageParser<RoleSurvivor>(() => new RoleSurvivor());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RoleSurvivor> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Roledata.RoledataReflection.Descriptor.MessageTypes[12]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleSurvivor() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleSurvivor(RoleSurvivor other) : this() {
      survivors_ = other.survivors_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleSurvivor Clone() {
      return new RoleSurvivor(this);
    }

    /// <summary>Field number for the "survivors" field.</summary>
    public const int SurvivorsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Survivor.Survivor> _repeated_survivors_codec
        = pb::FieldCodec.ForMessage(10, global::Survivor.Survivor.Parser);
    private readonly pbc::RepeatedField<global::Survivor.Survivor> survivors_ = new pbc::RepeatedField<global::Survivor.Survivor>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Survivor.Survivor> Survivors {
      get { return survivors_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RoleSurvivor);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RoleSurvivor other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!survivors_.Equals(other.survivors_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= survivors_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      survivors_.WriteTo(output, _repeated_survivors_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      survivors_.WriteTo(ref output, _repeated_survivors_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += survivors_.CalculateSize(_repeated_survivors_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RoleSurvivor other) {
      if (other == null) {
        return;
      }
      survivors_.Add(other.survivors_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            survivors_.AddEntriesFrom(input, _repeated_survivors_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            survivors_.AddEntriesFrom(ref input, _repeated_survivors_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RoleTeam : pb::IMessage<RoleTeam>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RoleTeam> _parser = new pb::MessageParser<RoleTeam>(() => new RoleTeam());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RoleTeam> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Roledata.RoledataReflection.Descriptor.MessageTypes[13]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleTeam() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleTeam(RoleTeam other) : this() {
      teams_ = other.teams_.Clone();
      defendTeams_ = other.defendTeams_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleTeam Clone() {
      return new RoleTeam(this);
    }

    /// <summary>Field number for the "teams" field.</summary>
    public const int TeamsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Fight.FormationTeam> _repeated_teams_codec
        = pb::FieldCodec.ForMessage(10, global::Fight.FormationTeam.Parser);
    private readonly pbc::RepeatedField<global::Fight.FormationTeam> teams_ = new pbc::RepeatedField<global::Fight.FormationTeam>();
    /// <summary>
    /// 布阵队伍
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Fight.FormationTeam> Teams {
      get { return teams_; }
    }

    /// <summary>Field number for the "defend_teams" field.</summary>
    public const int DefendTeamsFieldNumber = 2;
    private static readonly pb::FieldCodec<global::Fight.DefendTeam> _repeated_defendTeams_codec
        = pb::FieldCodec.ForMessage(18, global::Fight.DefendTeam.Parser);
    private readonly pbc::RepeatedField<global::Fight.DefendTeam> defendTeams_ = new pbc::RepeatedField<global::Fight.DefendTeam>();
    /// <summary>
    /// 防御队伍
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Fight.DefendTeam> DefendTeams {
      get { return defendTeams_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RoleTeam);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RoleTeam other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!teams_.Equals(other.teams_)) return false;
      if(!defendTeams_.Equals(other.defendTeams_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= teams_.GetHashCode();
      hash ^= defendTeams_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      teams_.WriteTo(output, _repeated_teams_codec);
      defendTeams_.WriteTo(output, _repeated_defendTeams_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      teams_.WriteTo(ref output, _repeated_teams_codec);
      defendTeams_.WriteTo(ref output, _repeated_defendTeams_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += teams_.CalculateSize(_repeated_teams_codec);
      size += defendTeams_.CalculateSize(_repeated_defendTeams_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RoleTeam other) {
      if (other == null) {
        return;
      }
      teams_.Add(other.teams_);
      defendTeams_.Add(other.defendTeams_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            teams_.AddEntriesFrom(input, _repeated_teams_codec);
            break;
          }
          case 18: {
            defendTeams_.AddEntriesFrom(input, _repeated_defendTeams_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            teams_.AddEntriesFrom(ref input, _repeated_teams_codec);
            break;
          }
          case 18: {
            defendTeams_.AddEntriesFrom(ref input, _repeated_defendTeams_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RoleDungeon : pb::IMessage<RoleDungeon>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RoleDungeon> _parser = new pb::MessageParser<RoleDungeon>(() => new RoleDungeon());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RoleDungeon> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Roledata.RoledataReflection.Descriptor.MessageTypes[14]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleDungeon() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleDungeon(RoleDungeon other) : this() {
      dungeonId_ = other.dungeonId_;
      accumulatedRewardAt_ = other.accumulatedRewardAt_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleDungeon Clone() {
      return new RoleDungeon(this);
    }

    /// <summary>Field number for the "dungeon_id" field.</summary>
    public const int DungeonIdFieldNumber = 1;
    private int dungeonId_;
    /// <summary>
    /// 已通关关卡 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int DungeonId {
      get { return dungeonId_; }
      set {
        dungeonId_ = value;
      }
    }

    /// <summary>Field number for the "accumulated_reward_at" field.</summary>
    public const int AccumulatedRewardAtFieldNumber = 2;
    private long accumulatedRewardAt_;
    /// <summary>
    /// 挂机奖励开始计算时间戳
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long AccumulatedRewardAt {
      get { return accumulatedRewardAt_; }
      set {
        accumulatedRewardAt_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RoleDungeon);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RoleDungeon other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (DungeonId != other.DungeonId) return false;
      if (AccumulatedRewardAt != other.AccumulatedRewardAt) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (DungeonId != 0) hash ^= DungeonId.GetHashCode();
      if (AccumulatedRewardAt != 0L) hash ^= AccumulatedRewardAt.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (DungeonId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(DungeonId);
      }
      if (AccumulatedRewardAt != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(AccumulatedRewardAt);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (DungeonId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(DungeonId);
      }
      if (AccumulatedRewardAt != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(AccumulatedRewardAt);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (DungeonId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(DungeonId);
      }
      if (AccumulatedRewardAt != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(AccumulatedRewardAt);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RoleDungeon other) {
      if (other == null) {
        return;
      }
      if (other.DungeonId != 0) {
        DungeonId = other.DungeonId;
      }
      if (other.AccumulatedRewardAt != 0L) {
        AccumulatedRewardAt = other.AccumulatedRewardAt;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            DungeonId = input.ReadInt32();
            break;
          }
          case 16: {
            AccumulatedRewardAt = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            DungeonId = input.ReadInt32();
            break;
          }
          case 16: {
            AccumulatedRewardAt = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RoleTask : pb::IMessage<RoleTask>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RoleTask> _parser = new pb::MessageParser<RoleTask>(() => new RoleTask());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RoleTask> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Roledata.RoledataReflection.Descriptor.MessageTypes[15]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleTask() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleTask(RoleTask other) : this() {
      taskList_ = other.taskList_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleTask Clone() {
      return new RoleTask(this);
    }

    /// <summary>Field number for the "task_list" field.</summary>
    public const int TaskListFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Task.TaskList> _repeated_taskList_codec
        = pb::FieldCodec.ForMessage(10, global::Task.TaskList.Parser);
    private readonly pbc::RepeatedField<global::Task.TaskList> taskList_ = new pbc::RepeatedField<global::Task.TaskList>();
    /// <summary>
    /// 任务列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Task.TaskList> TaskList {
      get { return taskList_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RoleTask);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RoleTask other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!taskList_.Equals(other.taskList_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= taskList_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      taskList_.WriteTo(output, _repeated_taskList_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      taskList_.WriteTo(ref output, _repeated_taskList_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += taskList_.CalculateSize(_repeated_taskList_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RoleTask other) {
      if (other == null) {
        return;
      }
      taskList_.Add(other.taskList_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            taskList_.AddEntriesFrom(input, _repeated_taskList_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            taskList_.AddEntriesFrom(ref input, _repeated_taskList_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RoleTech : pb::IMessage<RoleTech>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RoleTech> _parser = new pb::MessageParser<RoleTech>(() => new RoleTech());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RoleTech> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Roledata.RoledataReflection.Descriptor.MessageTypes[16]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleTech() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleTech(RoleTech other) : this() {
      techs_ = other.techs_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleTech Clone() {
      return new RoleTech(this);
    }

    /// <summary>Field number for the "techs" field.</summary>
    public const int TechsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Tech.Tech> _repeated_techs_codec
        = pb::FieldCodec.ForMessage(10, global::Tech.Tech.Parser);
    private readonly pbc::RepeatedField<global::Tech.Tech> techs_ = new pbc::RepeatedField<global::Tech.Tech>();
    /// <summary>
    /// 科技列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Tech.Tech> Techs {
      get { return techs_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RoleTech);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RoleTech other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!techs_.Equals(other.techs_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= techs_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      techs_.WriteTo(output, _repeated_techs_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      techs_.WriteTo(ref output, _repeated_techs_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += techs_.CalculateSize(_repeated_techs_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RoleTech other) {
      if (other == null) {
        return;
      }
      techs_.Add(other.techs_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            techs_.AddEntriesFrom(input, _repeated_techs_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            techs_.AddEntriesFrom(ref input, _repeated_techs_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RoleTrade : pb::IMessage<RoleTrade>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RoleTrade> _parser = new pb::MessageParser<RoleTrade>(() => new RoleTrade());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RoleTrade> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Roledata.RoledataReflection.Descriptor.MessageTypes[17]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleTrade() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleTrade(RoleTrade other) : this() {
      vanList_ = other.vanList_.Clone();
      todayTradeTimes_ = other.todayTradeTimes_;
      todayRobTimes_ = other.todayRobTimes_;
      buyExpressContractTimes_ = other.buyExpressContractTimes_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoleTrade Clone() {
      return new RoleTrade(this);
    }

    /// <summary>Field number for the "van_list" field.</summary>
    public const int VanListFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Trade.TradeCargoTransport> _repeated_vanList_codec
        = pb::FieldCodec.ForMessage(10, global::Trade.TradeCargoTransport.Parser);
    private readonly pbc::RepeatedField<global::Trade.TradeCargoTransport> vanList_ = new pbc::RepeatedField<global::Trade.TradeCargoTransport>();
    /// <summary>
    /// 我的货车列表，包括火车
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Trade.TradeCargoTransport> VanList {
      get { return vanList_; }
    }

    /// <summary>Field number for the "today_trade_times" field.</summary>
    public const int TodayTradeTimesFieldNumber = 2;
    private int todayTradeTimes_;
    /// <summary>
    /// 今日已贸易次数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int TodayTradeTimes {
      get { return todayTradeTimes_; }
      set {
        todayTradeTimes_ = value;
      }
    }

    /// <summary>Field number for the "today_rob_times" field.</summary>
    public const int TodayRobTimesFieldNumber = 3;
    private int todayRobTimes_;
    /// <summary>
    /// 今日已掠夺次数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int TodayRobTimes {
      get { return todayRobTimes_; }
      set {
        todayRobTimes_ = value;
      }
    }

    /// <summary>Field number for the "buy_express_contract_times" field.</summary>
    public const int BuyExpressContractTimesFieldNumber = 4;
    private int buyExpressContractTimes_;
    /// <summary>
    /// 特快合约购买次数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int BuyExpressContractTimes {
      get { return buyExpressContractTimes_; }
      set {
        buyExpressContractTimes_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RoleTrade);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RoleTrade other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!vanList_.Equals(other.vanList_)) return false;
      if (TodayTradeTimes != other.TodayTradeTimes) return false;
      if (TodayRobTimes != other.TodayRobTimes) return false;
      if (BuyExpressContractTimes != other.BuyExpressContractTimes) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= vanList_.GetHashCode();
      if (TodayTradeTimes != 0) hash ^= TodayTradeTimes.GetHashCode();
      if (TodayRobTimes != 0) hash ^= TodayRobTimes.GetHashCode();
      if (BuyExpressContractTimes != 0) hash ^= BuyExpressContractTimes.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      vanList_.WriteTo(output, _repeated_vanList_codec);
      if (TodayTradeTimes != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(TodayTradeTimes);
      }
      if (TodayRobTimes != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(TodayRobTimes);
      }
      if (BuyExpressContractTimes != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(BuyExpressContractTimes);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      vanList_.WriteTo(ref output, _repeated_vanList_codec);
      if (TodayTradeTimes != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(TodayTradeTimes);
      }
      if (TodayRobTimes != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(TodayRobTimes);
      }
      if (BuyExpressContractTimes != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(BuyExpressContractTimes);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += vanList_.CalculateSize(_repeated_vanList_codec);
      if (TodayTradeTimes != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(TodayTradeTimes);
      }
      if (TodayRobTimes != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(TodayRobTimes);
      }
      if (BuyExpressContractTimes != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(BuyExpressContractTimes);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RoleTrade other) {
      if (other == null) {
        return;
      }
      vanList_.Add(other.vanList_);
      if (other.TodayTradeTimes != 0) {
        TodayTradeTimes = other.TodayTradeTimes;
      }
      if (other.TodayRobTimes != 0) {
        TodayRobTimes = other.TodayRobTimes;
      }
      if (other.BuyExpressContractTimes != 0) {
        BuyExpressContractTimes = other.BuyExpressContractTimes;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            vanList_.AddEntriesFrom(input, _repeated_vanList_codec);
            break;
          }
          case 16: {
            TodayTradeTimes = input.ReadInt32();
            break;
          }
          case 24: {
            TodayRobTimes = input.ReadInt32();
            break;
          }
          case 32: {
            BuyExpressContractTimes = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            vanList_.AddEntriesFrom(ref input, _repeated_vanList_codec);
            break;
          }
          case 16: {
            TodayTradeTimes = input.ReadInt32();
            break;
          }
          case 24: {
            TodayRobTimes = input.ReadInt32();
            break;
          }
          case 32: {
            BuyExpressContractTimes = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// RolePrivilege 角色特权
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RolePrivilege : pb::IMessage<RolePrivilege>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RolePrivilege> _parser = new pb::MessageParser<RolePrivilege>(() => new RolePrivilege());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RolePrivilege> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Roledata.RoledataReflection.Descriptor.MessageTypes[18]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RolePrivilege() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RolePrivilege(RolePrivilege other) : this() {
      list_ = other.list_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RolePrivilege Clone() {
      return new RolePrivilege(this);
    }

    /// <summary>Field number for the "list" field.</summary>
    public const int ListFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Privilege.Privilege> _repeated_list_codec
        = pb::FieldCodec.ForMessage(10, global::Privilege.Privilege.Parser);
    private readonly pbc::RepeatedField<global::Privilege.Privilege> list_ = new pbc::RepeatedField<global::Privilege.Privilege>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Privilege.Privilege> List {
      get { return list_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RolePrivilege);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RolePrivilege other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!list_.Equals(other.list_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= list_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      list_.WriteTo(output, _repeated_list_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      list_.WriteTo(ref output, _repeated_list_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += list_.CalculateSize(_repeated_list_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RolePrivilege other) {
      if (other == null) {
        return;
      }
      list_.Add(other.list_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            list_.AddEntriesFrom(input, _repeated_list_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            list_.AddEntriesFrom(ref input, _repeated_list_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
