using System;
using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIRecruitRatioForm : UGuiFormEx
    {
        private class TabNode
        {
            public int index;
            public GameObject offBg;
            public GameObject onBg;
            public UIText offTxt;
            public UIText onTxt;
            public UIButton btn;
            public UnityAction callback;
        }
        
        private class CellNode
        {
            public string name;
            public int typeId;
            public float ratio;
            public List<int> cellList;
            public int cellWeightSum;
            public int priority;
        }
        
        private int _selectIndex; //选中的标签页索引
        private Transform tabRoot; //标签组的父节点
        
        private List<TabNode> _objList = new List<TabNode>();
        private Dictionary<int, UnityAction> _callbackDic = new Dictionary<int, UnityAction>();
        private Sequence sequenceObj;
        
        private List<CellNode> normalList;
        private List<CellNode> guaranteeList;
        private float spacing = 0;

        private RectTransform contentRect;
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            var layout = m_goContent.GetComponent<VerticalLayoutGroup>();
            if (layout != null)
            {
                spacing = layout.spacing;
            }
            contentRect= m_goContent.GetComponent<RectTransform>();
            normalList = new List<CellNode>();
            guaranteeList = new List<CellNode>();
            _selectIndex = -1;
            tabRoot = m_goGroup.transform;
            InitCount(2);
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            _selectIndex = -1;
            normalList.Clear();
            guaranteeList.Clear();
            _callbackDic.Clear();
            var recruitId = (int)userData;
            BindSelectCallback(0, 1100110);
            BindSelectCallback(1, 1100111);

            var manager = GameEntry.LogicData.RecruitData;
            var list = manager.GetRatioDropList(recruitId,out var ratioSum);
            for (var i = 0; i < list.Count; i++)
            {
                var info = list[i];
                var typeId = info.drop_group_id;
                var idList = manager.GetDropIdList(typeId, out int sum);
                normalList.Add( new CellNode()
                {
                    typeId = typeId, 
                    name = GetTitle(recruitId,i),
                    ratio = (float)info.precent/ratioSum,
                    cellList = idList,
                    cellWeightSum = sum,
                });
            }
            
            if (ToolScriptExtend.GetConfigById<Config.recruit_config>(recruitId, out var recruitData))
            {
                var idList1 = manager.GetDropIdList(recruitData.guarantee_drop, out int sum1);
                guaranteeList.Add( new CellNode()
                {
                    typeId = 0, 
                    name = GetTitle(recruitId,0),
                    ratio = 1,
                    cellList = idList1,
                    cellWeightSum = sum1,
                });
            }
            
            OnSelectLogic(0);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            sequenceObj?.Pause();
            sequenceObj?.Kill();

            normalList.Clear();
            guaranteeList.Clear();
            _callbackDic.Clear();
            _selectIndex = -1;
        }
        
        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnBackClick()
        {
            Close();
        }

        private void OnBtnCloseClick()
        {
            Close();
        }
        
        #region 切页标签
        /// <summary>
        /// 初始化调用
        /// </summary>
        /// <param name="count">标签的数量</param>
        public void InitCount(int count)
        {
            _objList.Clear();
            
            ToolScriptExtend.ClearAllChild(tabRoot);
            for (var i = 0; i < count; i++)
            {
                var obj = Instantiate(m_goTagItem, tabRoot);
                obj.SetActive(true);
                var trans = obj.transform;
                var node = new TabNode
                {
                    offTxt = trans.Find("btn/offBg/offTxt").GetComponent<UIText>(),
                    onTxt = trans.Find("btn/onBg/onTxt").GetComponent<UIText>(),
                    onBg = trans.Find("btn/onBg").gameObject,
                    offBg = trans.Find("btn/offBg").gameObject,
                    btn = trans.Find("btn").GetComponent<UIButton>(),
                };
                var index = i;
                BindBtnLogic(node.btn, () => { OnSelectLogic(index); });
                _objList.Add(node);
            }
        }

        /// <summary>
        /// 绑定指定页签索引的回调函数
        /// </summary>
        /// <param name="index">页签索引</param>
        /// <param name="txt">页签问本</param>
        /// <param name="callback">选中回调</param>
        public void BindSelectCallback(int index, int langId, UnityAction callback = null)
        {
            if (_callbackDic.ContainsKey(index))
            {
                Debug.LogError("已经注册过了！");
                return;
            }
            
            if (index < 0 || index > _objList.Count)
            {
                Debug.LogError("索引越界！");
                return;
            }

            var node = _objList[index];
            
            var str = ToolScriptExtend.GetLang(langId);
            node.offTxt.text = str;
            node.onTxt.text = str;
            
            _callbackDic.Add(index, callback);
        }

        /// <summary>
        /// 调用指定索引对应的回调函数
        /// </summary>
        /// <param name="index"></param>
        private void OnSelectLogic(int index)
        {
            if (index == _selectIndex)
            {
                return;
            }

            _selectIndex = index;

            var isExist = _callbackDic.TryGetValue(_selectIndex, out UnityAction callback);
            if (isExist)
            {
                callback?.Invoke();
                SetSelectStatus(index);
                if (_selectIndex == 0)
                {
                    ShowCellList(normalList);
                    
                }
                else if(_selectIndex == 1)
                {
                    ShowCellList(guaranteeList);
                }
            }

            m_scrollviewMain.normalizedPosition = new Vector2(0, 1);
        }

        /// <summary>
        /// 控制指定页签的UI表现
        /// </summary>
        /// <param name="index"></param>
        private void SetSelectStatus(int index)
        {
            var count = _objList.Count;
            for (var i = 0; i < count; i++)
            {
                var isActive = index == i;
                var node = _objList[i];
                node.onBg.SetActive(isActive);
                node.offBg.SetActive(!isActive);
            }
        }
        
        //绑定按钮点击回调
        private void BindBtnLogic(Button btn, UnityAction action)
        {
            btn.onClick.RemoveAllListeners();
            if (action != null)
            {
                btn.onClick.AddListener(action);
            }
        }
        #endregion
        
        private void ShowCellList(List<CellNode> list)
        {
            var root = m_goContent.transform;
            CalculateContentHeight(list);
            ToolScriptExtend.RecycleOrCreate(m_goDetailItem,root,list.Count);
            for (var i = 0; i < list.Count; i++)
            {
                var node = list[i];
                var child = root.GetChild(i);
                var obj = child.gameObject;
                var main = obj.transform.Find("main");
                var type = obj.transform.Find("type").GetComponent<UIText>();
                var ratio = obj.transform.Find("ratio").GetComponent<UIText>();
                
                type.text = node.name;
                ratio.text = String.Format("{0:#.0%}", node.ratio);
                AutoFigBg(obj, node.cellList.Count);
                
                node.cellList.Sort((a, b) =>
                {
                    var flag_a = GameEntry.LDLTable.GetTableById<Config.recruit_drops>(a);
                    var flag_b = GameEntry.LDLTable.GetTableById<Config.recruit_drops>(b);
                    return flag_b.view_order - flag_a.view_order;
                });

                var cellListCount = node.cellList.Count;
                ToolScriptExtend.RecycleOrCreate(m_goItem,main,cellListCount, (index,target) =>
                {
                    SetCellView(target, node.cellList[index],node.cellWeightSum,node.ratio);
                },0.01f);
            }
        }
        
        /// <summary>
        /// 根据传入数据设置cellItem显示
        /// </summary>
        /// <param name="cellObj"></param>
        private void SetCellView(GameObject cellObj,int dropId,int weightSum,float lastRatio)
        {
            var root = cellObj.transform;
            var quality = root.Find("quality").GetComponent<UIImage>();
            var icon = root.Find("icon").GetComponent<UIImage>();
            var count = root.Find("count").GetComponent<UIText>();
            var ratio = root.Find("ratio").GetComponent<UIText>();
            var newFlag = root.Find("newFlag").gameObject;
            var upFlag = root.Find("upFlag").gameObject;
            
            newFlag.SetActive(CheckNewHero(dropId));
            upFlag.SetActive(CheckUpHero(dropId));
            
            if (GameEntry.LDLTable.HaseTable<Config.recruit_drops>())
            {
                var data = GameEntry.LDLTable.GetTableById<Config.recruit_drops>(dropId);
                
                var rewardId = data.drop_reward.item_id;
                var rewardNum = data.drop_reward.num;
                count.text = ToolScriptExtend.FormatNumberWithUnit(rewardNum);
                ratio.text = Math.Round(((float)data.weight / weightSum)*100*lastRatio, 3) + "%";
                
                GameEntry.LogicData.RecruitData.GetItemData((int)rewardId, (itemData) =>
                {
                    quality.SetImage(ToolScriptExtend.GetQualityBg(itemData.quality));
                    icon.SetImage(itemData.icon);
                });
            };
        }
        
        //赛季可提升品质的英雄右上角有【UP!】标识
        private bool CheckUpHero(int dropId)
        {
            return false;
        }
        
        //新加入卡池英雄右上角有【NEW!】标识
        private bool CheckNewHero(int dropId)
        {
            if (GameEntry.LDLTable.HaseTable<Config.recruit_drops>())
            {
                var curTime = (int)TimeComponent.Now;
                var serviceTime = GameEntry.LDLNet.GetOpenServiceTimestamp();
                var data = GameEntry.LDLTable.GetTableById<Config.recruit_drops>(dropId);
                var offset = data.on_time+data.NEW_lasting_time;
                if (curTime < serviceTime + offset*86400)
                {
                    return true;
                }
            }
            return false;
        }

        //获取掉落组标题
        private string GetTitle(int id,int index)
        {
            if (GameEntry.LDLTable.HaseTable<Config.recruit_config>())
            {
                var data = GameEntry.LDLTable.GetTableById<Config.recruit_config>(id);
                if (data != null && data.drop_group_title.Count >= index+1)
                {
                    return ToolScriptExtend.GetLang(data.drop_group_title[index]) ;
                }
            }

            return "";
        }

        //计算Content高度
        private void CalculateContentHeight(List<CellNode> list)
        {
            float sum = 0;
            foreach (var node in list)
            {
                var height = CalculateContainerHeight(node.cellList.Count);
                sum += height;
            }
            sum += spacing * (list.Count - 1);
            contentRect.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical,sum);
        }
        
        //计算Container高度
        private int CalculateContainerHeight(int count)
        {
            var top = 60;
            var bottom = 50;
            var rowCount = GetRowCount(count);
            var height = rowCount * 200 + (rowCount - 1) * 48;
            height += top + bottom;
            return height;
        }
        
        private void AutoFigBg(GameObject obj,int count)
        {
            var top = 60;
            var bottom = 50;
            var rowCount = GetRowCount(count);
            var height = rowCount * 200 + (rowCount - 1) * 48;
            var main = obj.transform.Find("main").GetComponent<RectTransform>();
            main.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical,height);
            var whole = obj.GetComponent<RectTransform>();
            height += top + bottom;
            whole.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical,height);
        }
        
        //计算行数
        private int GetRowCount(int count)
        {
            var constraintCount = 5;
            var value1 = count / constraintCount;
            var value2 = count % constraintCount;
            var value3 = value2 > 0 ? 1 : 0;
            return value1 + value3;
        }
    }
}
