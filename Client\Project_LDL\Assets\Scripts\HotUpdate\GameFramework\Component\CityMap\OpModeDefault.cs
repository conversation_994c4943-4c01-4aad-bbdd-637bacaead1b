using UnityEngine;
using UnityEngine.EventSystems;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public class OpModeDefault : OpModeBase
    {
        private LayerMask buildMask;

        private const float LongPressThreshold = 1.5f; 
        private bool m_IsLongPressing = false;
        private float m_LongPressDuration = 0;
        private EL_Building m_LongPressBuilding = null;
        
        public OpModeDefault()
        {
            buildMask = ~0;
        }

        public override void OnDestroy()
        {
            
        }

        public bool IsLongPressing
        {
            get => m_IsLongPressing;
            set
            {
                if (m_IsLongPressing != value)
                {
                    Vector3 worldPosition = Vector3.zero;
                    if (m_LongPressBuilding != null)
                        m_LongPressBuilding.GetCenterWorldPos(out worldPosition);
                    Game.GameEntry.Event.Fire(this, OnBuildLongPressToMoveArgs.Create(value,worldPosition));
                }
                m_IsLongPressing = value;
            }
        }

        public override MapOpType GetOpType()
        {
            return MapOpType.DEFAULT_MODE;
        }

        public override void Update(float dt)
        {
            if (IsLongPressing)
            {
                if (m_LongPressDuration > LongPressThreshold)
                {
                    if (m_LongPressBuilding)
                    {
                        GameEntry.CityMap.BuildingBeginMove(m_LongPressBuilding);
                        m_LongPressBuilding = null;
                    }
                    //切换移动模式
                    Debug.Log("切换！！！！！");
                    IsLongPressing = false;
                    m_LongPressDuration = 0;
                }
                else
                {
                    m_LongPressDuration += dt;
                }
            }
        }

        protected override void OnDragged(PointerActionInfo pointer)
        {
            base.OnDragged(pointer);
            IsLongPressing = false;
            m_LongPressDuration = 0;
        }

        protected override void OnTap(PointerActionInfo pointer)
        {
            base.OnTap(pointer);
            // Debug.Log("OnTap");
            IsLongPressing = false;
            m_LongPressDuration = 0;
            if (pointer.startedOverUI) return;
            
            //射线检测
            EntityLogic el = RayCastObj(pointer.currentPosition);
            if (el)
            {
                el.OnClick();
            }
            GameEntry.Event.Fire(this, OnCityMapTapEventArgs.Create(el, pointer));
        }

        protected override void OnPressed(PointerActionInfo pointer)
        {
            base.OnPressed(pointer);
            if (pointer.startedOverUI) return;
            // Debug.Log("OnPressed");
            EntityLogic el = RayCastObj(pointer.currentPosition);
            if (el && el is EL_Building building)
            {
                if(building && building.CanMove())
                {
                    m_LongPressDuration = 0;
                    m_LongPressBuilding = building;
                    IsLongPressing = true;
                }
            }
        }

        protected override void OnReleased(PointerActionInfo pointer)
        {
            base.OnReleased(pointer);
            if (IsLongPressing)
            {
                IsLongPressing = false;
            }
        }

        private EntityLogic RayCastObj(Vector3 pos)
        {
            Ray ray =m_Param.camera.ScreenPointToRay(pos);
            RaycastHit hit;
            if (Physics.Raycast(ray, out hit, float.MaxValue, buildMask))
            {
                var name = hit.transform.gameObject.name;
                EntityLogic entityLogic = GetEntityByName(name, hit.transform);
                if (entityLogic)
                {
                    return entityLogic;
                }
            }

            return null;
        }

        private EntityLogic GetEntityByName(string name,Transform transform)
        {
            if (name.StartsWith("_building"))
            {
                if (transform.parent)
                {
                    EL_BuildingDisplay buildingDisplay = transform.parent.GetComponent<EL_BuildingDisplay>();
                    if (buildingDisplay != null)
                    {
                        EL_Building building = buildingDisplay.transform.parent.GetComponent<EL_Building>();
                        return building;
                    }
                }
            }
            else if (name.StartsWith("_area"))
            {
                if (transform.parent)
                {
                    var areaUnlock = transform.parent.GetComponent<EL_AreaUnlock>();
                    if (areaUnlock != null)
                    {
                        return areaUnlock;
                    }
                }
            }
            else if (name.StartsWith("_pvePathDisplay"))
            {
                if (transform.parent)
                {
                    EL_PvePathObjDisplay display = transform.parent.GetComponent<EL_PvePathObjDisplay>();
                    if (display != null)
                    {
                        EL_PvePath agent = display.transform.parent.GetComponent<EL_PvePath>();
                        return agent;
                    }
                }
            }
            else if (name.StartsWith("train_normal"))
            {
                if (transform)
                {
                    var train = transform.GetComponent<EL_Train>();
                    if (train != null)
                    {
                        return train;
                    }
                }
            }
            
            return default;
        }
    }
}