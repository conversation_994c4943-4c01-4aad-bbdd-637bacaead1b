// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: team.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Team {

  /// <summary>Holder for reflection information generated from team.proto</summary>
  public static partial class TeamReflection {

    #region Descriptor
    /// <summary>File descriptor for team.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static TeamReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Cgp0ZWFtLnByb3RvEgR0ZWFtKv8GCghUZWFtVHlwZRILCgd0ZWFtTmlsEAAS",
            "CAoEdGVzdBABEgwKCGNvbW1vbl8xEAsSDAoIY29tbW9uXzIQDBIMCghjb21t",
            "b25fMxANEgwKCGNvbW1vbl80EA4SDAoHZHVuZ2VvbhDpBxIYChNhcmVuYV9u",
            "b3ZpY2VfYXR0YWNrENsPEhgKE2FyZW5hX25vdmljZV9kZWZlbmQQ5Q8SFgoR",
            "YXJlbmFfcGVha19hdHRhY2sQ7w8SFgoRYXJlbmFfcGVha19kZWZlbmQQ+Q8S",
            "GgoVYXJlbmFfY29tcGV0X2F0dGFja18xEIMQEhoKFWFyZW5hX2NvbXBldF9h",
            "dHRhY2tfMhCEEBIaChVhcmVuYV9jb21wZXRfYXR0YWNrXzMQhRASGgoVYXJl",
            "bmFfY29tcGV0X2RlZmVuZF8xEI0QEhoKFWFyZW5hX2NvbXBldF9kZWZlbmRf",
            "MhCOEBIaChVhcmVuYV9jb21wZXRfZGVmZW5kXzMQjxASGQoUYXJlbmFfc3Rv",
            "cm1fYXR0YWNrXzEQlxASGQoUYXJlbmFfc3Rvcm1fYXR0YWNrXzIQmBASGQoU",
            "YXJlbmFfc3Rvcm1fYXR0YWNrXzMQmRASGQoUYXJlbmFfc3Rvcm1fZGVmZW5k",
            "XzEQoRASGQoUYXJlbmFfc3Rvcm1fZGVmZW5kXzIQohASGQoUYXJlbmFfc3Rv",
            "cm1fZGVmZW5kXzMQoxASGQoUdHJhZGVfdHJhaW5fYXR0YWNrXzEQwxcSGQoU",
            "dHJhZGVfdHJhaW5fYXR0YWNrXzIQxBcSGQoUdHJhZGVfdHJhaW5fYXR0YWNr",
            "XzMQxRcSGQoUdHJhZGVfdHJhaW5fZGVmZW5kXzEQzRcSGQoUdHJhZGVfdHJh",
            "aW5fZGVmZW5kXzIQzhcSGQoUdHJhZGVfdHJhaW5fZGVmZW5kXzMQzxcSFwoS",
            "dHJhZGVfdmFuX2F0dGFja18xENcXEhcKEnRyYWRlX3Zhbl9hdHRhY2tfMhDY",
            "FxIXChJ0cmFkZV92YW5fYXR0YWNrXzMQ2RcSFwoSdHJhZGVfdmFuX2F0dGFj",
            "a180ENoXEhcKEnRyYWRlX3Zhbl9kZWZlbmRfMRDhFxIXChJ0cmFkZV92YW5f",
            "ZGVmZW5kXzIQ4hcSFwoSdHJhZGVfdmFuX2RlZmVuZF8zEOMXEhcKEnRyYWRl",
            "X3Zhbl9kZWZlbmRfNBDkF0IXWhVzZXJ2ZXIvYXBpL3BiL3BiX3RlYW1iBnBy",
            "b3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Team.TeamType), }, null, null));
    }
    #endregion

  }
  #region Enums
  /// <summary>
  /// 队伍类型
  /// 每种功能的队伍进攻和防御各占 10 个位置
  /// 方便修改队伍时，自动判断队伍中的英雄不可重复上阵
  /// </summary>
  public enum TeamType {
    [pbr::OriginalName("teamNil")] TeamNil = 0,
    /// <summary>
    /// 测试使用
    /// </summary>
    [pbr::OriginalName("test")] Test = 1,
    /// <summary>
    /// 车队 1
    /// </summary>
    [pbr::OriginalName("common_1")] Common1 = 11,
    /// <summary>
    /// 车队 2
    /// </summary>
    [pbr::OriginalName("common_2")] Common2 = 12,
    /// <summary>
    /// 车队 3
    /// </summary>
    [pbr::OriginalName("common_3")] Common3 = 13,
    /// <summary>
    /// 车队 4
    /// </summary>
    [pbr::OriginalName("common_4")] Common4 = 14,
    /// <summary>
    /// 副本
    /// </summary>
    [pbr::OriginalName("dungeon")] Dungeon = 1001,
    /// <summary>
    /// 竞技场-新兵训练营-进攻
    /// </summary>
    [pbr::OriginalName("arena_novice_attack")] ArenaNoviceAttack = 2011,
    /// <summary>
    /// 竞技场-新兵训练营-防守
    /// </summary>
    [pbr::OriginalName("arena_novice_defend")] ArenaNoviceDefend = 2021,
    /// <summary>
    /// 竞技场-巅峰竞技场-进攻
    /// </summary>
    [pbr::OriginalName("arena_peak_attack")] ArenaPeakAttack = 2031,
    /// <summary>
    /// 竞技场-巅峰竞技场-防守
    /// </summary>
    [pbr::OriginalName("arena_peak_defend")] ArenaPeakDefend = 2041,
    /// <summary>
    /// 竞技场-3v3争霸赛-进攻-1
    /// </summary>
    [pbr::OriginalName("arena_compet_attack_1")] ArenaCompetAttack1 = 2051,
    /// <summary>
    /// 竞技场-3v3争霸赛-进攻-2
    /// </summary>
    [pbr::OriginalName("arena_compet_attack_2")] ArenaCompetAttack2 = 2052,
    /// <summary>
    /// 竞技场-3v3争霸赛-进攻-3
    /// </summary>
    [pbr::OriginalName("arena_compet_attack_3")] ArenaCompetAttack3 = 2053,
    /// <summary>
    /// 竞技场-3v3争霸赛-防守-1
    /// </summary>
    [pbr::OriginalName("arena_compet_defend_1")] ArenaCompetDefend1 = 2061,
    /// <summary>
    /// 竞技场-3v3争霸赛-防守-2
    /// </summary>
    [pbr::OriginalName("arena_compet_defend_2")] ArenaCompetDefend2 = 2062,
    /// <summary>
    /// 竞技场-3v3争霸赛-防守-3
    /// </summary>
    [pbr::OriginalName("arena_compet_defend_3")] ArenaCompetDefend3 = 2063,
    /// <summary>
    /// 竞技场-风暴竞技场-进攻-1
    /// </summary>
    [pbr::OriginalName("arena_storm_attack_1")] ArenaStormAttack1 = 2071,
    /// <summary>
    /// 竞技场-风暴竞技场-进攻-2
    /// </summary>
    [pbr::OriginalName("arena_storm_attack_2")] ArenaStormAttack2 = 2072,
    /// <summary>
    /// 竞技场-风暴竞技场-进攻-3
    /// </summary>
    [pbr::OriginalName("arena_storm_attack_3")] ArenaStormAttack3 = 2073,
    /// <summary>
    /// 竞技场-风暴竞技场-防守-1
    /// </summary>
    [pbr::OriginalName("arena_storm_defend_1")] ArenaStormDefend1 = 2081,
    /// <summary>
    /// 竞技场-风暴竞技场-防守-2
    /// </summary>
    [pbr::OriginalName("arena_storm_defend_2")] ArenaStormDefend2 = 2082,
    /// <summary>
    /// 竞技场-风暴竞技场-防守-3
    /// </summary>
    [pbr::OriginalName("arena_storm_defend_3")] ArenaStormDefend3 = 2083,
    /// <summary>
    /// 列车- 进攻-1
    /// </summary>
    [pbr::OriginalName("trade_train_attack_1")] TradeTrainAttack1 = 3011,
    /// <summary>
    /// 列车- 进攻-2
    /// </summary>
    [pbr::OriginalName("trade_train_attack_2")] TradeTrainAttack2 = 3012,
    /// <summary>
    /// 列车- 进攻-3
    /// </summary>
    [pbr::OriginalName("trade_train_attack_3")] TradeTrainAttack3 = 3013,
    /// <summary>
    /// 列车- 防守-1
    /// </summary>
    [pbr::OriginalName("trade_train_defend_1")] TradeTrainDefend1 = 3021,
    /// <summary>
    /// 列车- 防守-2
    /// </summary>
    [pbr::OriginalName("trade_train_defend_2")] TradeTrainDefend2 = 3022,
    /// <summary>
    /// 列车- 防守-3
    /// </summary>
    [pbr::OriginalName("trade_train_defend_3")] TradeTrainDefend3 = 3023,
    /// <summary>
    /// 货车- 进攻-1
    /// </summary>
    [pbr::OriginalName("trade_van_attack_1")] TradeVanAttack1 = 3031,
    /// <summary>
    /// 货车- 进攻-2
    /// </summary>
    [pbr::OriginalName("trade_van_attack_2")] TradeVanAttack2 = 3032,
    /// <summary>
    /// 货车- 进攻-3
    /// </summary>
    [pbr::OriginalName("trade_van_attack_3")] TradeVanAttack3 = 3033,
    /// <summary>
    /// 货车- 进攻-4
    /// </summary>
    [pbr::OriginalName("trade_van_attack_4")] TradeVanAttack4 = 3034,
    /// <summary>
    /// 货车- 防守-1
    /// </summary>
    [pbr::OriginalName("trade_van_defend_1")] TradeVanDefend1 = 3041,
    /// <summary>
    /// 货车- 防守-2
    /// </summary>
    [pbr::OriginalName("trade_van_defend_2")] TradeVanDefend2 = 3042,
    /// <summary>
    /// 货车- 防守-3
    /// </summary>
    [pbr::OriginalName("trade_van_defend_3")] TradeVanDefend3 = 3043,
    /// <summary>
    /// 货车- 防守-4
    /// </summary>
    [pbr::OriginalName("trade_van_defend_4")] TradeVanDefend4 = 3044,
  }

  #endregion

}

#endregion Designer generated code
